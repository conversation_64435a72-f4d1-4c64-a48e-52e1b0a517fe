/*!
 * <AUTHOR>
 * @date 2023-10-10
 * @email <EMAIL>
 * @brief XiaoMi Motors sdk
 */

#pragma once

#include "gsmp_v2-sdk/MotorBase_2.h"

namespace gsmp {
    class MiMotor : public MotorBase {
    public:
        MiMotor();
        ~MiMotor() override = default;

        bool enableAllDevices() override;

        bool disableAllDevices() override;

        void parseSlaverMessages(EtherCAT_RxMsg *rxMessage, int slaveIdx) override;

        void setMotorCommand(std::array<MotorCommand, 12> command) override;

        void setServoCommand(std::array<ServoCommand, 2> command);

        void printResponse() override;

        void getMotorsCanID();

    protected:
        uint8_t mCanMasterId;
        uint8_t mMotorId[12];
    };
}