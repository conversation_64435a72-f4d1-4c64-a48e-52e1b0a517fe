# Install script for directory: /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/legged_control/legged_examples/legged_robs3go/legged_robs3go_description

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REG<PERSON> REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "RelWithDebInfo")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/usr/bin/objdump")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  
      if (NOT EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}")
        file(MAKE_DIRECTORY "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}")
      endif()
      if (NOT EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/.catkin")
        file(WRITE "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/.catkin" "")
      endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/_setup_util.py")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install" TYPE PROGRAM FILES "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/_setup_util.py")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/env.sh")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install" TYPE PROGRAM FILES "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/env.sh")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/setup.bash;/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/local_setup.bash")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install" TYPE FILE FILES
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/setup.bash"
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/local_setup.bash"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/setup.sh;/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/local_setup.sh")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install" TYPE FILE FILES
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/setup.sh"
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/local_setup.sh"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/setup.zsh;/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/local_setup.zsh")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install" TYPE FILE FILES
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/setup.zsh"
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/local_setup.zsh"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/setup.fish;/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/local_setup.fish")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install" TYPE FILE FILES
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/setup.fish"
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/local_setup.fish"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install/.rosinstall")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  file(INSTALL DESTINATION "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/install" TYPE FILE FILES "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/.rosinstall")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/pkgconfig" TYPE FILE FILES "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/legged_robs3go_description.pc")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/share/legged_robs3go_description/cmake" TYPE FILE FILES
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/legged_robs3go_descriptionConfig.cmake"
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/catkin_generated/installspace/legged_robs3go_descriptionConfig-version.cmake"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/share/legged_robs3go_description" TYPE FILE FILES "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/legged_control/legged_examples/legged_robs3go/legged_robs3go_description/package.xml")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/share/legged_robs3go_description" TYPE DIRECTORY FILES
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/legged_control/legged_examples/legged_robs3go/legged_robs3go_description/meshes"
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/legged_control/legged_examples/legged_robs3go/legged_robs3go_description/urdf"
    "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/legged_control/legged_examples/legged_robs3go/legged_robs3go_description/launch"
    )
endif()

if(NOT CMAKE_INSTALL_LOCAL_ONLY)
  # Include the install script for each subdirectory.
  include("/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/gtest/cmake_install.cmake")

endif()

if(CMAKE_INSTALL_COMPONENT)
  set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
file(WRITE "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
