set(_CATKIN_CURRENT_PACKAGE "legged_robs3go_description")
set(legged_robs3go_description_VERSION "0.0.0")
set(legged_robs3go_description_MAINTAINER "<PERSON><PERSON><PERSON> <<EMAIL>>")
set(legged_robs3go_description_PACKAGE_FORMAT "2")
set(legged_robs3go_description_BUILD_DEPENDS )
set(legged_robs3go_description_BUILD_EXPORT_DEPENDS )
set(legged_robs3go_description_BUILDTOOL_DEPENDS "catkin")
set(legged_robs3go_description_BUILDTOOL_EXPORT_DEPENDS )
set(legged_robs3go_description_EXEC_DEPENDS "xacro" "joint_state_publisher" "robot_state_publisher")
set(legged_robs3go_description_RUN_DEPENDS "xacro" "joint_state_publisher" "robot_state_publisher")
set(legged_robs3go_description_TEST_DEPENDS )
set(legged_robs3go_description_DOC_DEPENDS )
set(legged_robs3go_description_URL_WEBSITE "")
set(legged_robs3go_description_URL_BUGTRACKER "")
set(legged_robs3go_description_URL_REPOSITORY "")
set(legged_robs3go_description_DEPRECATED "")