# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "".split(';') if "" != "" else []
PROJECT_CATKIN_DEPENDS = "".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "legged_robs3go_description"
PROJECT_SPACE_DIR = "/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/legged_robs3go_description"
PROJECT_VERSION = "0.0.0"
