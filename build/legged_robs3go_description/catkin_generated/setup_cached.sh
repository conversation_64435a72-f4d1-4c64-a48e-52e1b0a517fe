#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/legged_robs3go_description:$CMAKE_PREFIX_PATH"
export PWD='/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description'
export ROSLISP_PACKAGE_DIRECTORIES="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/legged_robs3go_description/share/common-lisp:$ROSLISP_PACKAGE_DIRECTORIES"
export ROS_PACKAGE_PATH="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/legged_control/legged_examples/legged_robs3go/legged_robs3go_description:$ROS_PACKAGE_PATH"