/usr/bin/c++ -fPIC -O2 -g -DNDEBUG -shared -Wl,-soname,libgsmp_v2_sdk.so.0.0.3 -o /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/gsmp_v2_sdk/lib/libgsmp_v2_sdk.so.0.0.3 CMakeFiles/gsmp_v2_sdk.dir/src/EtherCatMaster_2.cpp.o CMakeFiles/gsmp_v2_sdk.dir/src/MiMotor_2.cpp.o CMakeFiles/gsmp_v2_sdk.dir/src/MotorBase_2.cpp.o CMakeFiles/gsmp_v2_sdk.dir/src/MotorUtils.cpp.o  -Wl,-rpath,/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/gsmp_v2_sdk/third-party/soem:/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/gsmp_v2_sdk/third-party/serial -lm -lrt -lpthread third-party/soem/libsoem.so third-party/serial/libserial.so -lrt -lpthread 
