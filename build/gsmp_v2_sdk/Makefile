# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/gsmp-sdk/gsmp_robsDog-sdk/gsmp_v2-sdk

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/gsmp_v2_sdk

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/usr/local/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/gsmp_v2_sdk/CMakeFiles /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/gsmp_v2_sdk//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/gsmp_v2_sdk/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named download_extra_data

# Build rule for target.
download_extra_data: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 download_extra_data
.PHONY : download_extra_data

# fast build rule for target.
download_extra_data/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/download_extra_data.dir/build.make CMakeFiles/download_extra_data.dir/build
.PHONY : download_extra_data/fast

#=============================================================================
# Target rules for targets named tests

# Build rule for target.
tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 tests
.PHONY : tests

# fast build rule for target.
tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/tests.dir/build.make CMakeFiles/tests.dir/build
.PHONY : tests/fast

#=============================================================================
# Target rules for targets named run_tests

# Build rule for target.
run_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 run_tests
.PHONY : run_tests

# fast build rule for target.
run_tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_tests.dir/build.make CMakeFiles/run_tests.dir/build
.PHONY : run_tests/fast

#=============================================================================
# Target rules for targets named clean_test_results

# Build rule for target.
clean_test_results: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean_test_results
.PHONY : clean_test_results

# fast build rule for target.
clean_test_results/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/clean_test_results.dir/build.make CMakeFiles/clean_test_results.dir/build
.PHONY : clean_test_results/fast

#=============================================================================
# Target rules for targets named doxygen

# Build rule for target.
doxygen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 doxygen
.PHONY : doxygen

# fast build rule for target.
doxygen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/doxygen.dir/build.make CMakeFiles/doxygen.dir/build
.PHONY : doxygen/fast

#=============================================================================
# Target rules for targets named _catkin_empty_exported_target

# Build rule for target.
_catkin_empty_exported_target: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 _catkin_empty_exported_target
.PHONY : _catkin_empty_exported_target

# fast build rule for target.
_catkin_empty_exported_target/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/_catkin_empty_exported_target.dir/build.make CMakeFiles/_catkin_empty_exported_target.dir/build
.PHONY : _catkin_empty_exported_target/fast

#=============================================================================
# Target rules for targets named gsmp_v2_sdk

# Build rule for target.
gsmp_v2_sdk: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gsmp_v2_sdk
.PHONY : gsmp_v2_sdk

# fast build rule for target.
gsmp_v2_sdk/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/build
.PHONY : gsmp_v2_sdk/fast

#=============================================================================
# Target rules for targets named gmock

# Build rule for target.
gmock: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock
.PHONY : gmock

# fast build rule for target.
gmock/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googlemock/CMakeFiles/gmock.dir/build.make gtest/googlemock/CMakeFiles/gmock.dir/build
.PHONY : gmock/fast

#=============================================================================
# Target rules for targets named gmock_main

# Build rule for target.
gmock_main: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gmock_main
.PHONY : gmock_main

# fast build rule for target.
gmock_main/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googlemock/CMakeFiles/gmock_main.dir/build.make gtest/googlemock/CMakeFiles/gmock_main.dir/build
.PHONY : gmock_main/fast

#=============================================================================
# Target rules for targets named gtest

# Build rule for target.
gtest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gtest
.PHONY : gtest

# fast build rule for target.
gtest/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googletest/CMakeFiles/gtest.dir/build.make gtest/googletest/CMakeFiles/gtest.dir/build
.PHONY : gtest/fast

#=============================================================================
# Target rules for targets named gtest_main

# Build rule for target.
gtest_main: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 gtest_main
.PHONY : gtest_main

# fast build rule for target.
gtest_main/fast:
	$(MAKE) $(MAKESILENT) -f gtest/googletest/CMakeFiles/gtest_main.dir/build.make gtest/googletest/CMakeFiles/gtest_main.dir/build
.PHONY : gtest_main/fast

#=============================================================================
# Target rules for targets named soem

# Build rule for target.
soem: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 soem
.PHONY : soem

# fast build rule for target.
soem/fast:
	$(MAKE) $(MAKESILENT) -f third-party/soem/CMakeFiles/soem.dir/build.make third-party/soem/CMakeFiles/soem.dir/build
.PHONY : soem/fast

#=============================================================================
# Target rules for targets named serial

# Build rule for target.
serial: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 serial
.PHONY : serial

# fast build rule for target.
serial/fast:
	$(MAKE) $(MAKESILENT) -f third-party/serial/CMakeFiles/serial.dir/build.make third-party/serial/CMakeFiles/serial.dir/build
.PHONY : serial/fast

src/EtherCatMaster_2.o: src/EtherCatMaster_2.cpp.o
.PHONY : src/EtherCatMaster_2.o

# target to build an object file
src/EtherCatMaster_2.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/EtherCatMaster_2.cpp.o
.PHONY : src/EtherCatMaster_2.cpp.o

src/EtherCatMaster_2.i: src/EtherCatMaster_2.cpp.i
.PHONY : src/EtherCatMaster_2.i

# target to preprocess a source file
src/EtherCatMaster_2.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/EtherCatMaster_2.cpp.i
.PHONY : src/EtherCatMaster_2.cpp.i

src/EtherCatMaster_2.s: src/EtherCatMaster_2.cpp.s
.PHONY : src/EtherCatMaster_2.s

# target to generate assembly for a file
src/EtherCatMaster_2.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/EtherCatMaster_2.cpp.s
.PHONY : src/EtherCatMaster_2.cpp.s

src/MiMotor_2.o: src/MiMotor_2.cpp.o
.PHONY : src/MiMotor_2.o

# target to build an object file
src/MiMotor_2.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MiMotor_2.cpp.o
.PHONY : src/MiMotor_2.cpp.o

src/MiMotor_2.i: src/MiMotor_2.cpp.i
.PHONY : src/MiMotor_2.i

# target to preprocess a source file
src/MiMotor_2.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MiMotor_2.cpp.i
.PHONY : src/MiMotor_2.cpp.i

src/MiMotor_2.s: src/MiMotor_2.cpp.s
.PHONY : src/MiMotor_2.s

# target to generate assembly for a file
src/MiMotor_2.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MiMotor_2.cpp.s
.PHONY : src/MiMotor_2.cpp.s

src/MotorBase_2.o: src/MotorBase_2.cpp.o
.PHONY : src/MotorBase_2.o

# target to build an object file
src/MotorBase_2.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MotorBase_2.cpp.o
.PHONY : src/MotorBase_2.cpp.o

src/MotorBase_2.i: src/MotorBase_2.cpp.i
.PHONY : src/MotorBase_2.i

# target to preprocess a source file
src/MotorBase_2.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MotorBase_2.cpp.i
.PHONY : src/MotorBase_2.cpp.i

src/MotorBase_2.s: src/MotorBase_2.cpp.s
.PHONY : src/MotorBase_2.s

# target to generate assembly for a file
src/MotorBase_2.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MotorBase_2.cpp.s
.PHONY : src/MotorBase_2.cpp.s

src/MotorUtils.o: src/MotorUtils.cpp.o
.PHONY : src/MotorUtils.o

# target to build an object file
src/MotorUtils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MotorUtils.cpp.o
.PHONY : src/MotorUtils.cpp.o

src/MotorUtils.i: src/MotorUtils.cpp.i
.PHONY : src/MotorUtils.i

# target to preprocess a source file
src/MotorUtils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MotorUtils.cpp.i
.PHONY : src/MotorUtils.cpp.i

src/MotorUtils.s: src/MotorUtils.cpp.s
.PHONY : src/MotorUtils.s

# target to generate assembly for a file
src/MotorUtils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/gsmp_v2_sdk.dir/build.make CMakeFiles/gsmp_v2_sdk.dir/src/MotorUtils.cpp.s
.PHONY : src/MotorUtils.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... _catkin_empty_exported_target"
	@echo "... clean_test_results"
	@echo "... download_extra_data"
	@echo "... doxygen"
	@echo "... run_tests"
	@echo "... tests"
	@echo "... gmock"
	@echo "... gmock_main"
	@echo "... gsmp_v2_sdk"
	@echo "... gtest"
	@echo "... gtest_main"
	@echo "... serial"
	@echo "... soem"
	@echo "... src/EtherCatMaster_2.o"
	@echo "... src/EtherCatMaster_2.i"
	@echo "... src/EtherCatMaster_2.s"
	@echo "... src/MiMotor_2.o"
	@echo "... src/MiMotor_2.i"
	@echo "... src/MiMotor_2.s"
	@echo "... src/MotorBase_2.o"
	@echo "... src/MotorBase_2.i"
	@echo "... src/MotorBase_2.s"
	@echo "... src/MotorUtils.o"
	@echo "... src/MotorUtils.i"
	@echo "... src/MotorUtils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

