#!/usr/bin/env sh
# generated from catkin/python/catkin/environment_cache.py

# based on a snapshot of the environment before and after calling the setup script
# it emulates the modifications of the setup script without recurring computations

# new environment variables

# modified environment variables
export CMAKE_PREFIX_PATH="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/gsmp_v2_sdk:$CMAKE_PREFIX_PATH"
export LD_LIBRARY_PATH="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/gsmp_v2_sdk/lib:$LD_LIBRARY_PATH"
export PKG_CONFIG_PATH="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/gsmp_v2_sdk/lib/pkgconfig:$PKG_CONFIG_PATH"
export PWD='/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/gsmp_v2_sdk'
export ROSLISP_PACKAGE_DIRECTORIES="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/gsmp_v2_sdk/share/common-lisp:$ROSLISP_PACKAGE_DIRECTORIES"
export ROS_PACKAGE_PATH="/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/gsmp-sdk/gsmp_ysc-sdk/gsmp_v2-sdk:/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/gsmp-sdk/gsmp_robsDog-sdk/gsmp_v2-sdk:$ROS_PACKAGE_PATH"