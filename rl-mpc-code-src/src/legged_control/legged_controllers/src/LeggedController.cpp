//
// Created by qia<PERSON> on 2022/6/24.
//

#include <pinocchio/fwd.hpp>  // forward declarations must be included first.

#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/kinematics.hpp>

#include "legged_controllers/LeggedController.h"

#include <ocs2_centroidal_model/AccessHelperFunctions.h>
#include <ocs2_centroidal_model/CentroidalModelPinocchioMapping.h>
#include <ocs2_core/thread_support/ExecuteAndSleep.h>
#include <ocs2_core/thread_support/SetThreadPriority.h>
// #include <ocs2_legged_robot_ros/gait/GaitReceiver.h>
#include <ocs2_msgs/mpc_observation.h>
#include <ocs2_pinocchio_interface/PinocchioEndEffectorKinematics.h>
#include <ocs2_ros_interfaces/common/RosMsgConversions.h>
#include <ocs2_ros_interfaces/synchronized_module/RosReferenceManager.h>
#include <ocs2_sqp/SqpMpc.h>
#include <ocs2_robotic_tools/common/RotationDerivativesTransforms.h>

#include <angles/angles.h>
#include <legged_estimation/FromTopiceEstimate.h>
#include <legged_estimation/LinearKalmanFilter.h>
#include <legged_estimation/ComputeForOdom.h>
#include <legged_wbc/HierarchicalWbc.h>
#include <legged_wbc/WeightedWbc.h>
#include "legged_wbc/TestWbc.h"
#include <pluginlib/class_list_macros.hpp>
#include "std_msgs/Float64MultiArray.h"
#include "legged_common/params.h"
#include "legged_controllers/utilities.h"


namespace henning {
  void pseudoInverse2(Eigen::MatrixXd const& matrix, double sigmaThreshold, // 0.001
                    Eigen::MatrixXd& invMatrix) {
    if ((1 == matrix.rows()) && (1 == matrix.cols())) {
      invMatrix.resize(1, 1);
      if (matrix.coeff(0, 0) > sigmaThreshold) {
        invMatrix.coeffRef(0, 0) = 1.0 / matrix.coeff(0, 0);
      } else {
        invMatrix.coeffRef(0, 0) = 0.0;
      }
      return;  
    }

    Eigen::JacobiSVD<Eigen::MatrixXd> svd(matrix,Eigen::ComputeThinU | Eigen::ComputeThinV);
    // not sure if we need to svd.sort()... probably not
    int const nrows(svd.singularValues().rows());
    Eigen::MatrixXd invS;
    invS = Eigen::MatrixXd::Zero(nrows, nrows);
    for (int ii(0); ii < nrows; ++ii) {
      if (svd.singularValues().coeff(ii) > sigmaThreshold) {
        invS.coeffRef(ii, ii) = 1.0 / svd.singularValues().coeff(ii);
      } else {
        // invS.coeffRef(ii, ii) = 1.0/ sigmaThreshold;
        // printf("sigular value is too small: %f\n",
        // svd.singularValues().coeff(ii));
      }
    }
    invMatrix = svd.matrixV() * invS * svd.matrixU().transpose();
  }
}


namespace legged {
std::atomic<scalar_t> kp_stance{0};
std::atomic<scalar_t> kd_stance{3};
std::atomic<scalar_t> kp_swing{50};
std::atomic<scalar_t> kd_swing{3};
std::atomic<scalar_t> kp_lifted{80};
std::atomic<scalar_t> kd_lifted{1};
double comHeightRef = 0.34;

void dynamicParamCallback(legged_controllers::TutorialsConfig &config, uint32_t level) {
  kp_stance = config.kp_stance;
  kp_swing = config.kp_swing;
  kd_stance = config.kd_stance;
  kd_swing = config.kd_swing;
}

bool LeggedController::init(hardware_interface::RobotHW* robot_hw, ros::NodeHandle& controller_nh) {
  // Dynamic server
  server_ptr_ = std::make_unique<dynamic_reconfigure::Server<legged_controllers::TutorialsConfig>>(ros::NodeHandle("controller"));
  dynamic_reconfigure::Server<legged_controllers::TutorialsConfig>::CallbackType f;
  f = boost::bind(&dynamicParamCallback, _1, _2);
  server_ptr_->setCallback(f);
  // Initialize OCS2
  std::string urdfFile;
  std::string taskFile;
  std::string referenceFile;
  std::string motorFile;
  std::string velLimitFile;
  std::string bodyRecoveryFile;

  controller_nh.getParam("/urdfFile", urdfFile);
  controller_nh.getParam("/taskFile", taskFile);
  controller_nh.getParam("/referenceFile", referenceFile);
  controller_nh.getParam("/motorFile", motorFile);
  controller_nh.getParam("/velLimitFile", velLimitFile);
  controller_nh.getParam("/bodyRecoveryFile", bodyRecoveryFile);


  bool verbose = false;

  desTransModeJointPos_ = vector_t::Zero(12);
  desGetDownModeJointPos_ = vector_t::Zero(12);
  desSitDownModeJointPos_ = vector_t::Zero(12);
  desHandShakeLeftModeJointPos_ = vector_t::Zero(12);
  desHandShakeRightModeJointPos_ = vector_t::Zero(12);  
  standupModeJointPos_ = vector_t::Zero(12);  
  dt_rl_xyz = vector_t::Zero(3); 

  loadData::loadEigenMatrix(referenceFile, "trans_des_pos.getdown", desGetDownModeJointPos_);
  loadData::loadEigenMatrix(referenceFile, "trans_des_pos.sitdown_mode", desSitDownModeJointPos_);
  loadData::loadEigenMatrix(referenceFile, "trans_des_pos.handshake_left", desHandShakeLeftModeJointPos_);
  loadData::loadEigenMatrix(referenceFile, "trans_des_pos.handshake_right", desHandShakeRightModeJointPos_);
  loadData::loadEigenMatrix(referenceFile, "trans_des_pos.standup", standupModeJointPos_);

  loadData::loadCppDataType(velLimitFile, "rl_dt_line_x", dt_rl_xyz(0));
  loadData::loadCppDataType(velLimitFile, "rl_dt_line_y", dt_rl_xyz(1));
  loadData::loadCppDataType(velLimitFile, "rl_dt_ang_z", dt_rl_xyz(2));

  loadData::loadCppDataType(taskFile, "legged_robot_interface.verbose", verbose);
  loadData::loadCppDataType(taskFile, "model_settings.gravityCompensation", gravity_compensation_flag);

  loadData::loadCppDataType(taskFile, "swing_trajectory_config.feet_bias_x", feet_bias_x);
  loadData::loadCppDataType(taskFile, "swing_trajectory_config.feet_bias_y", feet_bias_y);
  loadData::loadCppDataType(taskFile, "swing_trajectory_config.feet_bias_z", feet_bias_z);

  loadData::loadCppDataType(taskFile, "lifted_check_config.max_foot_pos_z", max_foot_pos_z_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.max_leg_length", max_leg_length_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.gravity_factor", gravity_factor_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.phase_factor", phase_factor_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.debug_print", debug_print_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.gravity_factor_lifted_recover", gravity_factor_lifted_recover);

  loadData::loadCppDataType(taskFile, "lifted_check_config.hip_pos_lifted", hip_pos_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.thigh_pos_lifted", thigh_pos_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.calf_pos_lifted", calf_pos_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.mode_transition_time", mode_transition_time_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.pos_transition_time", pos_transition_time_lifted);
  loadData::loadCppDataType(taskFile, "lifted_check_config.detection_enable", lifted_detection_enable);
  loadData::loadCppDataType(taskFile, "lifted_check_config.k_lose_contact_factor", k_lose_contact_factor_);

  loadData::loadCppDataType(referenceFile, "comHeight", comHeightRef);

  hugMeBabyModeJointPos_ = vector_t::Zero(12);
  loadData::loadEigenMatrix(taskFile, "hugMeBabyConfig.jointPosConfig", hugMeBabyModeJointPos_);
  loadData::loadCppDataType(taskFile, "hugMeBabyConfig.motorConfig.kp", kp_hug_me_baby_);
  loadData::loadCppDataType(taskFile, "hugMeBabyConfig.motorConfig.kd", kd_hug_me_baby_);

  // 鬼能想到这三天搞了这堆垃圾——henning, 我打赌10天以后就忘了怎么搞了
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.thigh_length", thigh_length_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.calf_length", calf_length_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.calf_link_pitch", calf_link_pitch_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_thigh_pos_adjust_stage_f", des_thigh_pos_adjust_stage_f_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_calf_pos_adjust_stage_f", des_calf_pos_adjust_stage_f_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_thigh_pos_adjust_stage_r", des_thigh_pos_adjust_stage_r_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_calf_pos_adjust_stage_r", des_calf_pos_adjust_stage_r_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_adjust_stage", kp_adjust_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_adjust_stage", kd_adjust_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.adjust_duration", adjust_duration_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.push_force_init", push_force_init_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.push_force_target", push_force_target_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.push_duration", push_duration_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.exert_force_duration", exert_force_duration_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.rear_legs_adjust_duration", rear_legs_adjust_duration_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.rear_legs_keep_duration", rear_legs_keep_duration_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.torque_rear_thigh_push_stage", torque_rear_thigh_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.torque_rear_calf_push_stage", torque_rear_calf_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.torque_rear_thigh_keep_stage", torque_rear_thigh_keep_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_front_hip_pos_push_stage", des_front_hip_pos_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_front_thigh_pos_push_stage", des_front_thigh_pos_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_front_calf_pos_push_stage", des_front_calf_pos_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_back_thigh_pos_push_stage", des_back_thigh_pos_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_back_calf_pos_push_stage", des_back_calf_pos_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_hip_exert_stage", kp_hip_exert_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_hip_exert_stage", kd_hip_exert_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_hip_push_stage", kp_hip_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_thigh_push_stage", kp_thigh_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_calf_push_stage", kp_calf_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_hip_push_stage", kd_hip_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_thigh_push_stage", kd_thigh_push_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_calf_push_stage", kd_calf_push_stage_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_hip_push_stage_rear", kp_hip_push_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_thigh_push_stage_rear", kp_thigh_push_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_calf_push_stage_rear", kp_calf_push_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_hip_push_stage_rear", kd_hip_push_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_thigh_push_stage_rear", kd_thigh_push_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_calf_push_stage_rear", kd_calf_push_stage_rear_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_hip_push_adjust_stage_rear", kp_hip_push_adjust_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_thigh_push_adjust_stage_rear", kp_thigh_push_adjust_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_calf_push_adjust_stage_rear", kp_calf_push_adjust_stage_rear_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_hip_push_adjust_stage_rear1", kd_hip_push_adjust_stage_rear1_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_thigh_push_adjust_stage_rear1", kd_thigh_push_adjust_stage_rear1_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_calf_push_adjust_stage_rear1", kd_calf_push_adjust_stage_rear1_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_hip_push_adjust_stage_rear2", kd_hip_push_adjust_stage_rear2_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_thigh_push_adjust_stage_rear2", kd_thigh_push_adjust_stage_rear2_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_calf_push_adjust_stage_rear2", kd_calf_push_adjust_stage_rear2_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.happy_new_year_pos1_x", happy_new_year_pos1_x_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.happy_new_year_pos1_y", happy_new_year_pos1_y_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.happy_new_year_pos1_z", happy_new_year_pos1_z_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.happy_new_year_pos2_x", happy_new_year_pos2_x_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.happy_new_year_pos2_y", happy_new_year_pos2_y_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.happy_new_year_pos2_z", happy_new_year_pos2_z_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.happy_new_year_swing_duration", happy_new_year_swing_duration_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_hip_final_stage", kp_hip_final_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_thigh_final_stage", kp_thigh_final_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_calf_final_stage", kp_calf_final_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_hip_final_stage", kd_hip_final_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_thigh_final_stage", kd_thigh_final_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_calf_final_stage", kd_calf_final_stage_);

  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_thigh_pos_recover_stage", des_thigh_pos_recover_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.des_calf_pos_recover_stage", des_calf_pos_recover_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kp_recover_stage", kp_recover_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.kd_recover_stage", kd_recover_stage_);
  loadData::loadCppDataType(taskFile, "two_leg_stand_config.recovery_duration", recovery_duration_);

  singleLegKinematic_.setLinkLength({0, thigh_length_, calf_length_});
  // 鬼能想到这三天搞了这堆垃圾——henning, 我打赌10天以后就忘了怎么搞了

  // 后空翻相关配置
  boost::property_tree::ptree pt;
  boost::property_tree::read_info(taskFile, pt);
  std::string back_flip_traj_path;
  loadData::loadPtreeValue(pt, back_flip_traj_path, "offline_optimization_config.back_flip_traj_path", false);
  // loadData::loadCppDataType(taskFile, "back_flip_config.kp", kp_back_flip_);
  dataReader_ = new DataReader(back_flip_traj_path);
  backFlipCtrl_ = new BackFlipCtrl<double>(dataReader_, 0.002);

  loadData::loadCppDataType(taskFile, "offline_optimization_config.kp_adjust_stage_back_flip", kp_adjust_stage_back_flip_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.kd_adjust_stage_back_flip", kd_adjust_stage_back_flip_);

  loadData::loadCppDataType(taskFile, "offline_optimization_config.adjust_duration", back_flip_adjust_duration_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.adjust_thigh_pos", back_flip_adjust_thigh_pos_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.adjust_calf_pos", back_flip_adjust_calf_pos_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.landing_thigh_pos_back_flip_f", landing_thigh_pos_back_flip_f_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.landing_knee_pos_back_flip_f", landing_knee_pos_back_flip_f_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.landing_thigh_pos_back_flip_b", landing_thigh_pos_back_flip_b_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.landing_knee_pos_back_flip_b", landing_knee_pos_back_flip_b_);

  loadData::loadCppDataType(taskFile, "offline_optimization_config.kp_run_stage_back_flip", kp_run_stage_back_flip_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.kd_run_stage_back_flip", kd_run_stage_back_flip_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.torque_mult_back_flip", torque_mult_back_flip_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.kp_final_stage_back_flip", kp_final_stage_back_flip_);
  loadData::loadCppDataType(taskFile, "offline_optimization_config.kd_final_stage_back_flip", kd_final_stage_back_flip_);

  backFlipCtrl_->SetParameter(kp_run_stage_back_flip_, kd_run_stage_back_flip_, torque_mult_back_flip_, kp_final_stage_back_flip_, kd_final_stage_back_flip_,
                              landing_thigh_pos_back_flip_f_, landing_knee_pos_back_flip_f_, landing_thigh_pos_back_flip_b_, landing_knee_pos_back_flip_b_);

  backFlipCmd_ = new LegControllerCommand<double>[4];
  backFlipData_ = new LegControllerData<double>[4];

  enterBackFlipModeJointPos_ = vector_t::Zero(12);
  cmdBackFlipModeJointPos_ = vector_t::Zero(12);
  cmdBackFlipModeJointVel_ = vector_t::Zero(12);
  cmdBackFlipModeTau_ = vector_t::Zero(12);
  cmdBackFlipModeKp_ = vector_t::Zero(12);
  cmdBackFlipModeKd_ = vector_t::Zero(12);
  // 后空翻相关配置

  //RL controller
  RLController_init(robot_hw,controller_nh);

  setupMotorParam(motorFile);
  std::cout<<" #### kp,kd = "<<kp_stance<<" "<<kd_stance<<" "<<kp_swing<<" "<<kd_swing<<std::endl;

  setupLeggedInterface(taskFile, urdfFile, referenceFile,verbose );
  setupMpc();
  setupMrt();
  // Visualization
  ros::NodeHandle nh;
  CentroidalModelPinocchioMapping pinocchioMapping(leggedInterface_->getCentroidalModelInfo());
  eeKinematicsPtr_ = std::make_shared<PinocchioEndEffectorKinematics>(leggedInterface_->getPinocchioInterface(), pinocchioMapping,
                                                                      leggedInterface_->modelSettings().contactNames3DoF);
  robotVisualizer_ = std::make_shared<LeggedRobotVisualizer>(leggedInterface_->getPinocchioInterface(),
                                                             leggedInterface_->getCentroidalModelInfo(), *eeKinematicsPtr_, nh);
  selfCollisionVisualization_.reset(new LeggedSelfCollisionVisualization(leggedInterface_->getPinocchioInterface(),
                                                                         leggedInterface_->getGeometryInterface(), pinocchioMapping, nh));

  // Hardware interface
  auto* hybridJointInterface = robot_hw->get<HybridJointInterface>();
  std::vector<std::string> joint_names{"LF_HAA", "LF_HFE", "LF_KFE", "LH_HAA", "LH_HFE", "LH_KFE",
                                       "RF_HAA", "RF_HFE", "RF_KFE", "RH_HAA", "RH_HFE", "RH_KFE"};
  for (const auto& joint_name : joint_names) {
    hybridJointHandles_.push_back(hybridJointInterface->getHandle(joint_name));
  }
  jointTor_ = vector_t::Zero(hybridJointHandles_.size());
  jointPos_ = vector_t::Zero(hybridJointHandles_.size());
  jointVel_ = vector_t::Zero(hybridJointHandles_.size());
  cmdLiftedJointPos_ = vector_t::Zero(hybridJointHandles_.size());
  enterLiftedJointPos_ = vector_t::Zero(hybridJointHandles_.size());
  cmdPosModeJointPos_ = vector_t::Zero(hybridJointHandles_.size());
  enterPosModeJointPos_ = vector_t::Zero(hybridJointHandles_.size());

  enterTwoLegStandModeJointPos_ = vector_t::Zero(hybridJointHandles_.size());
  cmdTwoLegStandModeJointPos_ = vector_t::Zero(hybridJointHandles_.size());
  cmdTwoLegStandModeTau_ = vector_t::Zero(hybridJointHandles_.size());
  cmdTwoLegStandModeKp_ = vector_t::Zero(hybridJointHandles_.size());
  cmdTwoLegStandModeKd_ = vector_t::Zero(hybridJointHandles_.size());
  
  enterPawupJointPos_ = vector_t::Zero(hybridJointHandles_.size());
  desPawupJointPosUp_ = vector_t::Zero(hybridJointHandles_.size());
  desPawupJointPosDown_ = vector_t::Zero(hybridJointHandles_.size());
  

  // add for recovery
  recoveryCurPos = vector_t::Zero(hybridJointHandles_.size());
  recoveryTargetPos = vector_t::Zero(hybridJointHandles_.size());
  shrinkTargetPos = vector_t::Zero(hybridJointHandles_.size());
  straightenTargetPos = vector_t::Zero(hybridJointHandles_.size());
  reverseStage1TargetPos = vector_t::Zero(hybridJointHandles_.size());
  reverseStage2TargetPos = vector_t::Zero(hybridJointHandles_.size());
  reverseStage3TargetPos = vector_t::Zero(hybridJointHandles_.size());
  reverseStage4TargetPos = vector_t::Zero(hybridJointHandles_.size());
  reverseStage5TargetPos = vector_t::Zero(hybridJointHandles_.size());
  recoveryStageTime = vector_t::Zero(7);
  loadData::loadEigenMatrix(bodyRecoveryFile, "shrinkJointState", shrinkTargetPos);
  loadData::loadEigenMatrix(bodyRecoveryFile, "straightenJointState", straightenTargetPos);
  loadData::loadEigenMatrix(bodyRecoveryFile, "reverseStage1JointState", reverseStage1TargetPos);
  loadData::loadEigenMatrix(bodyRecoveryFile, "reverseStage2JointState", reverseStage2TargetPos);
  loadData::loadEigenMatrix(bodyRecoveryFile, "reverseStage3JointState", reverseStage3TargetPos);
  loadData::loadEigenMatrix(bodyRecoveryFile, "reverseStage4JointState", reverseStage4TargetPos);
  loadData::loadEigenMatrix(bodyRecoveryFile, "reverseStage5JointState", reverseStage5TargetPos);
  loadData::loadEigenMatrix(bodyRecoveryFile, "recovery_time", recoveryStageTime);


  auto* contactInterface = robot_hw->get<ContactSensorInterface>();
  for (const auto& name : leggedInterface_->modelSettings().contactNames3DoF) {
    contactHandles_.push_back(contactInterface->getHandle(name));
  }
  imuSensorHandle_ = robot_hw->get<hardware_interface::ImuSensorInterface>()->getHandle("base_imu");

  // State estimation
  setupStateEstimate(taskFile, verbose);

  // RL controller
  setupStateEstimateForRL(taskFile, verbose);
  RLController_init(robot_hw,controller_nh);

  // for gravity compensation test
  if (gravity_compensation_flag) 
    wbc_ = std::make_shared<TestWbc>(leggedInterface_->getPinocchioInterface(), leggedInterface_->getCentroidalModelInfo(),
                                       *eeKinematicsPtr_);
  // Whole body control
  else   
    wbc_ = std::make_shared<WeightedWbc>(leggedInterface_->getPinocchioInterface(), leggedInterface_->getCentroidalModelInfo(),
                                       *eeKinematicsPtr_);
  wbc_->loadTasksSetting(taskFile, verbose);

  // Safety Checker
  safetyChecker_ = std::make_shared<SafetyChecker>(leggedInterface_->getCentroidalModelInfo());

  eeKinematicsPtr_->setPinocchioInterface(leggedInterface_->getPinocchioInterface());
  qPino_.resize(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum);
  vPino_.resize(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum);
  qPino_RL_.resize(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum);
  vPino_RL_.resize(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum);

  // Control Data Analysis
  estBodyPositionPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_body_position", 1);
  estRLBodyPositionPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_RLbody_position", 1);
  cmdBodyPositionPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/cmd_body_position", 1);
  mpcPlannedBodyPositionPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/mpc_planned_body_position", 1);
  
  wbcPlannedBodyAccelerationPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/wbc_planned_body_acceleration", 1);
  imuMeasuredBodyAccelerationPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/imu_measured_body_acceleration", 1);
  estContactForcePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_contact_force", 1);

  estFeetPositionPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_feet_position", 1);
  estRLFeetPositionPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_RLfeet_position", 1);
  mpcPlannedFeetPositionPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/mpc_planned_feet_position", 1);

  estFeetVelocityPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_feet_velocity", 1);
  estRLFeetVelocityPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_RLfeet_velocity", 1);
  mpcPlannedFeetVelocityPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/mpc_planned_feet_velocity", 1);
 
  mpcPlannedContactForcePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/mpc_planned_contact_force", 1);
  wbcPlannedContactForcePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/wbc_planned_contact_force", 1);

  cmdContactStatePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/cmd_contact_state", 1);
  estContactStatePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_contact_state", 1);
  realContactStatePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/real_contact_state", 1);
  
  wbcPlannedTorquePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/wbc_planned_torque", 1);
  outputTorquePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/output_torque", 1);
  realTorquePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/real_torque", 1);
  estDisturbanceTorquePublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/est_disturbance_torque", 1);

  realJointVelPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/real_joint_vel", 1);
  mpcPlannedJointVelPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/mpc_planned_joint_vel", 1);
  wbcPlannedJointVelPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/wbc_planned_joint_vel", 1);
  filteredPlannedJointVelPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/filtered_planned_joint_vel", 1);

  realJointPosPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/real_joint_pos", 1);
  mpcPlannedJointPosPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/mpc_planned_joint_pos", 1);
  wbcPlannedJointPosPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/wbc_planned_joint_pos", 1);
  filteredPlannedJointPosPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/filtered_planned_joint_pos", 1);

  wbcPlannedJointAccPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/wbc_planned_joint_acc", 1);
  testLiftedPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/test_lifted_msg", 1);

  // Emergency Stop
  sub_emgstop = nh.subscribe<std_msgs::Float32>("/emergency_stop", 1, &LeggedController::EmergencyStopCallback, this);
  sub_loadcontroller = nh.subscribe<std_msgs::Float32>("/load_controller", 1, &LeggedController::LoadControllerCallback, this);
  sub_testLiftedTopic = nh.subscribe<std_msgs::Float32>("/test_lifted_topic", 1, &LeggedController::testLiftedTopic, this);
  custom_position_control_sub_ = nh.subscribe<std_msgs::Float64MultiArray>("/custom_position", 1, &LeggedController::customPositionControlCallback, this);
  two_legs_stand_sub_ = nh.subscribe<std_msgs::Float32>("/two_legs_stand", 1, &LeggedController::twoLegsStandCallback, this);
  back_flip_sub_ = nh.subscribe<std_msgs::Float32>("/back_flip", 1, &LeggedController::backFlipCallback, this);
  // add by fzy: roll_recovery
  roll_recovery_sub_ = nh.subscribe<std_msgs::Float32>("/roll_recovery", 1, &LeggedController::rollRecoveryCallback, this);


  //
  gsmp_quadbotSate_sub_ = nh.subscribe<gsmp_msgs::gl_quadbotState>("/gsmp_msgs/gl_quadbotState", 1, &LeggedController::quadbotStateCallback, this);
  gsmp_quadbotCmd_pub_ = nh.advertise<gsmp_msgs::gl_quadbotCmd>("/gsmp_msgs/gl_quadbotCmd", 1);

  // add rl passive mode
  gait_type_sub_ = nh.subscribe<std_msgs::Int32MultiArray>("/gait_type", 1, &LeggedController::gaitTypeCallback, this);

  // add for record data by fzy
  bodyGobalVelPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/bodyGobalVel", 1);
  bodyLocalVelPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/bodyLocalVel", 1);

  dataRecordSub_ = nh.subscribe<std_msgs::Float32>("/data_record", 1, &LeggedController::dataRecordActionCallback, this);
  std::string ampDataName_ = "/home/<USER>/record_amp_data.txt";
  // std::ofstream ofs_amp;
  ofs_amp.open(ampDataName_, std::ios::trunc);

  //odom
  auto MpcOdomCallback = [this](const nav_msgs::Odometry::ConstPtr& msg) {
    mpcodom_msg_.header = msg->header;
    mpcodom_msg_.pose.pose.position.x = msg->pose.pose.position.x;
    mpcodom_msg_.pose.pose.position.y = msg->pose.pose.position.y;
    mpcodom_msg_.pose.pose.position.z = msg->pose.pose.position.z;
    mpcodom_msg_.pose.pose.orientation = msg->pose.pose.orientation;
    mpcodom_msg_.pose.covariance = msg->pose.covariance;
    mpcodom_msg_.twist.twist.angular.x = msg->twist.twist.angular.x;
    mpcodom_msg_.twist.twist.angular.y = msg->twist.twist.angular.y;
    mpcodom_msg_.twist.twist.angular.z = msg->twist.twist.angular.z;
    mpcodom_msg_.twist.twist.linear.x = msg->twist.twist.linear.x;
    mpcodom_msg_.twist.twist.linear.y = msg->twist.twist.linear.y;
    mpcodom_msg_.twist.twist.linear.z = msg->twist.twist.linear.z;
    mpcodom_msg_.twist.covariance = msg->twist.covariance;
  };
  mpc_odom_sub_ = nh.subscribe<nav_msgs::Odometry>("/mpc_odom", 1, MpcOdomCallback);

  auto RlOdomCallback = [this](const nav_msgs::Odometry::ConstPtr& msg) {
    rlodom_msg_.header = msg->header;
    rlodom_msg_.pose.pose.position.x = msg->pose.pose.position.x;
    rlodom_msg_.pose.pose.position.y = msg->pose.pose.position.y;
    rlodom_msg_.pose.pose.position.z = msg->pose.pose.position.z;
    rlodom_msg_.pose.pose.orientation = msg->pose.pose.orientation;
    rlodom_msg_.pose.covariance = msg->pose.covariance;
    rlodom_msg_.twist.twist.angular.x = msg->twist.twist.angular.x;
    rlodom_msg_.twist.twist.angular.y = msg->twist.twist.angular.y;
    rlodom_msg_.twist.twist.angular.z = msg->twist.twist.angular.z;
    rlodom_msg_.twist.twist.linear.x = msg->twist.twist.linear.x;
    rlodom_msg_.twist.twist.linear.y = msg->twist.twist.linear.y;
    rlodom_msg_.twist.twist.linear.z = msg->twist.twist.linear.z;
    rlodom_msg_.twist.covariance = msg->twist.covariance;
  };
  rl_odom_sub_ = nh.subscribe<nav_msgs::Odometry>("/rl_odom", 1, RlOdomCallback);

  odom_pub_ = nh.advertise<nav_msgs::Odometry>("/odom", 1);


  // for stair_mode, add by henning on 2024-1-17
  // auto stair_mode_callback = [this](const std_msgs::Int32::ConstPtr& msg) {
  //   lifted_detection_enable = !lifted_detection_enable;  
  //   if(lifted_detection_enable)z
  //     std::cout<<" *****************stair_mode_ open, close lifted detection" <<std::endl;quadbotStateCallback
  //   else
  //     std::cout<<" *****************stair_mode_ close, open lifted detection" <<std::endl;
  // };
  // // add by fzy:   离地检测关闭状态下不支持通过/stair_mode来修改离地检测开关
  // if(lifted_detection_enable)
  //   stair_mode_sub_ = nh.subscribe<std_msgs::Int32>("/stair_mode", 1, stair_mode_callback);

  // adjust Centroid
  adjustCentroid_pub_ = nh.advertise<std_msgs::Float32>("/move_base_simple/adjustCentroid", 1);
  plane_est_pub = nh.advertise<std_msgs::Float64MultiArray>("/terrain/plane_est", 1);
  rot_state_sub_ = nh.subscribe<gsmp_msgs::legged_robot_state>("/legged_robot_state", 1, &LeggedController::RotStateCallBack, this);
  ros::param::set("gsmp_trans_model_state","done");
  ros::param::set("robotBodyRecoveryState","true");

  for(auto& elem : mpcContactForceErrCorNum_)
  {
    elem.resize(2);  
  }
  for(auto& elem : jointTorErrCorNum_)
  {
    elem.resize(2);  
  }
  // added by henning on 2023-10-7
  plane_est_msg_.data.resize(8);
  plane_est_msg_.data[0] = 0;
  plane_est_msg_.data[1] = 0;
  plane_est_msg_.data[2] = 0;
  plane_est_msg_.data[3] = 0;
  plane_est_msg_.data[4] = 1;
  plane_est_msg_.data[5] = 0;
  plane_est_msg_.data[6] = 0;
  plane_est_msg_.data[7] = 0;

  W_pla_ << 1, 0, 0,
            1, 0, 0,
            1, 0, 0,
            1, 0, 0;

  return true;
}

bool LeggedController::RLController_init(hardware_interface::RobotHW *robotHw, ros::NodeHandle &controllerNH){
  std::vector<std::string> jointNames{"LF_HAA", "LF_HFE", "LF_KFE",
                                      "RF_HAA", "RF_HFE", "RF_KFE",
                                      "LH_HAA", "LH_HFE", "LH_KFE",
                                      "RH_HAA", "RH_HFE", "RH_KFE"};
  std::vector<std::string> footNames{"LF_FOOT", "LH_FOOT", "RF_FOOT", "RH_FOOT"};
  actuatedDofNum_ = jointNames.size();




  ros::NodeHandle nh;
  cmdTime = ros::Time::now();
  mode_ = Mode::WALK;

  rlComputeTauPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/output_torque", 1);
  rlComputePosPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/output_pos", 1);
  rlPosPublisher_ = nh.advertise<std_msgs::Float64MultiArray>("data_analysis/rlPos", 1);

  cmdVelSub_ = nh.subscribe("/cmd_vel", 1, &LeggedController::cmdVelCallback, this);
  controller_switch_sub = nh.subscribe<std_msgs::Float32>("/controller_switch", 1, &LeggedController::ControllerSwitchCallback, this);

  // Load policy model and rl cfg
  if (!loadModel(controllerNH))
  {
    ROS_ERROR_STREAM("[RLControllerBase] Failed to load the model. Ensure the path is correct and accessible.");
    return false;
  }
  if (!loadRLCfg(controllerNH))
  {
    ROS_ERROR_STREAM("[RLControllerBase] Failed to load the rl config. Ensure the yaml is correct and accessible.");
    return false;
  }

  standJointAngles_.resize(actuatedDofNum_);
  lieJointAngles_.resize(actuatedDofNum_);
  auto &StandState = standjointState_;
  auto &LieState = liejointState_;

  joint_dim_ = actuatedDofNum_;
  
  // 加载kd
  nh.getParam("/Kd_config/kd", cfg_kd);

    // Stand Lie joint
  lieJointAngles_ << LieState.LF_HAA_joint, LieState.LF_HFE_joint, LieState.LF_KFE_joint,
      LieState.RF_HAA_joint, LieState.RF_HFE_joint, LieState.RF_KFE_joint,
      LieState.LH_HAA_joint, LieState.LH_HFE_joint, LieState.LH_KFE_joint,
      LieState.RH_HAA_joint, LieState.RH_HFE_joint, LieState.RH_KFE_joint;

  standJointAngles_ << StandState.LF_HAA_joint, StandState.LF_HFE_joint, StandState.LF_KFE_joint,
      StandState.RF_HAA_joint, StandState.RF_HFE_joint, StandState.RF_KFE_joint,
      StandState.LH_HAA_joint, StandState.LH_HFE_joint, StandState.LH_KFE_joint,
      StandState.RH_HAA_joint, StandState.RH_HFE_joint, StandState.RH_KFE_joint;
  

    // switchMode
    auto switchModeCallback = [this](const std_msgs::Float32::ConstPtr &msg)
    {
      ros::Duration t(0.8);
      if (ros::Time::now() - switchTime > t && controller_switch_flag)
      {
        if (start_control == true)
        {
          if (mode_ == Mode::STAND)
          {
            standPercent_ = 0;
            for (size_t i = 0; i < hybridJointHandles_.size(); i++)
            {
              currentJointAngles_[i] = hybridJointHandles_[i].getPosition();
            }
            mode_ = Mode::LIE;
            ROS_INFO("STAND2LIE");
          }
          else if (mode_ == Mode::LIE || mode_== Mode::DEFAULT)
          {
            standPercent_ = 0;
            for (size_t i = 0; i < hybridJointHandles_.size(); i++)
            {
              currentJointAngles_[i] = hybridJointHandles_[i].getPosition();
            }
            mode_ = Mode::STAND;
            ROS_INFO("LIE2STAND");
          }
        }
        switchTime = ros::Time::now();
      }
    };
    switchModeSub_ = nh.subscribe<std_msgs::Float32>("/RL_switch_mode", 1, switchModeCallback);

    // walkMode
    auto walkModeCallback = [this](const std_msgs::Float32::ConstPtr &msg)
    {
      ros::Duration t(0.2);
      if (ros::Time::now() - switchTime > t && controller_switch_flag)
      {
        if (mode_ == Mode::STAND || mode_ == Mode::UPSTAIR)
        {
          mode_ = Mode::WALK;
          RLType = 0;
          ROS_INFO("switched To WALK");
        }
        switchTime = ros::Time::now();
      }
    };
    walkModeSub_ = nh.subscribe<std_msgs::Float32>("/RL_walk_mode", 1, walkModeCallback);


    // rlstandMode
    auto rlstandModeCallback = [this](const std_msgs::Float32::ConstPtr &msg)
    {
      ros::Duration t(0.2);
      if (ros::Time::now() - switchTime > t)
      {
        if (mode_ == Mode::WALK)
        {
          mode_ = Mode::RLSTAND;
          ROS_INFO("WALK2RLSTAND"); // 这里
        }
        else if (mode_ == Mode::RLSTAND)
        {
          mode_ = Mode::WALK;
          ROS_INFO("RLSTAND2WALK"); 
        }

        switchTime = ros::Time::now();
      }
    };
    rlstandModeSub_ = controllerNH.subscribe<std_msgs::Float32>("/rlstand_mode", 1, rlstandModeCallback);


    // positionMode
    auto positionModeCallback = [this](const std_msgs::Float32::ConstPtr &msg)
    {
      ros::Duration t(0.2);
      if (ros::Time::now() - switchTime > t && controller_switch_flag)
      {
        if (mode_ == Mode::WALK || mode_ == Mode::UPSTAIR)
        {
          mode_ = Mode::STAND;
          ROS_INFO("WALK2STAND");
        }
        else if (mode_ == Mode::DEFAULT)
        {
          standPercent_ = 0;
          for (size_t i = 0; i < hybridJointHandles_.size(); i++)
          {
            currentJointAngles_[i] = hybridJointHandles_[i].getPosition();
          }
          mode_ = Mode::LIE;
          ROS_INFO("DEF2LIE");
        }

        switchTime = ros::Time::now();
      }
    };
    positionCtrlSub_ = nh.subscribe<std_msgs::Float32>("/RL_position_control", 1, positionModeCallback);

    // upstairMode
    // auto upstairModeCallback = [this](const gsmp_msgs::gl_stairmodeCmd& msg)
    // {
    //       //ROS_INFO("I heard: [%s]", msg->data.c_str());
    //   ROS_INFO("Data of stair are %d, %0.3f, %0.3f, %0.3f, %0.3f", msg.classification, 
    //                                                                 msg.depth,
    //                                                                 msg.x_coord,
    //                                                                 msg.y_coord,
    //                                                                 msg.z_coord );
    //   //classification: 0

    //   ros::Duration t(0.2);
    //   if (ros::Time::now() - switchTime > t && controller_switch_flag)
    //   {
    //     if (mode_ == Mode::STAND || mode_ == Mode::WALK)
    //     {
    //       mode_ = Mode::UPSTAIR;
    //       RLType = 1;
    //       ROS_INFO("switched To UPSTAIR");
    //     }
    //     switchTime = ros::Time::now();
    //   }
    // };
    // upstairModeSub_ = nh.subscribe<gsmp_msgs::gl_stairmodeCmd>("/RL_stair_mode", 1, upstairModeCallback);
    upstairModeSub_ = nh.subscribe("/RL_stair_mode", 1000, &LeggedController::StairSwitchCallback, this);

    return true;
}

void LeggedController::starting(const ros::Time& time) {
// add
  ros::param::set("gsmp_controller_state","start");
  // std::cout<<"starting time"<<time.toSec()<<std::endl;
  // Initial state
  currentObservation_.state.setZero(leggedInterface_->getCentroidalModelInfo().stateDim);
  
  updateStateEstimation(time, ros::Duration(0.002));
  updateStateEstimationForRL(time, ros::Duration(0.002));
  mpc_zyx.setZero();
  rl_zyx.setZero();
  
  currentObservation_.input.setZero(leggedInterface_->getCentroidalModelInfo().inputDim);
  currentObservation_.mode = ModeNumber::STANCE;

  TargetTrajectories target_trajectories({currentObservation_.time}, {currentObservation_.state}, {currentObservation_.input});

  // Set the first observation and command and wait for optimization to finish
  mpcMrtInterface_->setCurrentObservation(currentObservation_);
  mpcMrtInterface_->getReferenceManager().setTargetTrajectories(target_trajectories);
  ROS_INFO_STREAM("Waiting for the initial policy ...");
  while (!mpcMrtInterface_->initialPolicyReceived() && ros::ok()) {
    mpcMrtInterface_->advanceMpc();
    ros::WallRate(leggedInterface_->mpcSettings().mrtDesiredFrequency_).sleep();
  }
  ROS_INFO_STREAM("Initial policy has been received.");

  mpcRunning_ = true;
  controlTimer_.startTimer();
  pos_des_filtered_ = vector_t::Zero(12);
  vel_des_filtered_ = vector_t::Zero(12);
  tau_des_filtered_ = vector_t::Zero(12);
  MpcContactForce_ = vector_t::Zero(12);

  //RL controller add by WangC 2014/09/30
  pos_des_output_.resize(joint_dim_);
  vel_des_output_.resize(joint_dim_);
  pos_des_output_.setZero();
  vel_des_output_.setZero();

  mode_ = Mode::DEFAULT;
  currentJointAngles_.resize(hybridJointHandles_.size());
  scalar_t durationSecs = 2.0;
  standDuration_ = durationSecs * 500.0;
  standPercent_ = 0;
  loopCount_ = 0;


  // // for roll-recvery
  // enterPassiveModel_ = false;
  // runningPositionMode = false;
  // runningLiftedMode = false;
  // runningTwoLegStandMode = false;
  // runningTransMode = false;
  // runningBackFlipMode = false;
  // controller_switch_flag = false; //mpc
  // curRotState = "getdown";
  // curControState = "on";


  std::cout<<"starting"<<std::endl;

}

void LeggedController::update(const ros::Time& time, const ros::Duration& period) {
  // const ros::Time shifted_time = time - starting_time_;
  // State Estimate
  // updateStateEstimation(time, period);

  //emtimation for odom
  vector3_t odom_zyx;
  nav_msgs::Odometry odom;

  odom.header.stamp = time;
  odom.header.frame_id = "odom";
  odom.child_frame_id = "base";
  
  if(!controller_switch_flag){
    updateStateEstimation(time, period);
    Eigen::Quaternion<scalar_t> q_mpc_;
    q_mpc_ = Eigen::Quaternion<scalar_t>(mpcodom_msg_.pose.pose.orientation.w,mpcodom_msg_.pose.pose.orientation.x,
     mpcodom_msg_.pose.pose.orientation.y, mpcodom_msg_.pose.pose.orientation.z);
    mpc_zyx = quatToZyx(q_mpc_);
    odom.pose.pose.position.z = mpcodom_msg_.pose.pose.position.z;
    odom.pose.covariance = mpcodom_msg_.pose.covariance;
    odom.twist = mpcodom_msg_.twist;
  }
  else {
    updateStateEstimationForRL(time, period);
    Eigen::Quaternion<scalar_t> q_rl_;
    q_rl_ = Eigen::Quaternion<scalar_t>(rlodom_msg_.pose.pose.orientation.w,rlodom_msg_.pose.pose.orientation.x,
     rlodom_msg_.pose.pose.orientation.y, rlodom_msg_.pose.pose.orientation.z);
    rl_zyx = quatToZyx(q_rl_);
    odom.pose.pose.position.z = rlodom_msg_.pose.pose.position.z;
    odom.pose.covariance = rlodom_msg_.pose.covariance;
    odom.twist = rlodom_msg_.twist;
  }

  odom_zyx << mpc_zyx(2) + rl_zyx(2), mpc_zyx(1) + rl_zyx(1), mpc_zyx(0) + rl_zyx(0);
  odom_quat_ = ZyxToquat(odom_zyx);
  odom.pose.pose.position.x = mpcodom_msg_.pose.pose.position.x + rlodom_msg_.pose.pose.position.x;
  odom.pose.pose.position.y = mpcodom_msg_.pose.pose.position.y + rlodom_msg_.pose.pose.position.y;
  odom.pose.pose.orientation.x = odom_quat_.x();
  odom.pose.pose.orientation.y = odom_quat_.y();
  odom.pose.pose.orientation.z = odom_quat_.z();
  odom.pose.pose.orientation.w = odom_quat_.w();

  odom_pub_.publish(odom);

  vector_t cmd_body_pos = mpcMrtInterface_->getReferenceManager().getTargetTrajectories().
    stateTrajectory[0].segment(6, 6);
  vector_t cmd_body_vel = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getCmdBodyVel();
  cmd_body_vel(2) = 0;  // z
  cmd_body_vel(4) = 0;  // pitch
  cmd_body_vel(5) = 0;  // roll

  // publish data for analysis
  estBodyPositionPublisher_.publish(createFloat64MultiArrayFromVector(
    currentObservation_.state.segment(6, 6)));
  estRLBodyPositionPublisher_.publish(createFloat64MultiArrayFromVector(
    currentObservation_RL_.state.segment(6, 6)));
  cmdBodyPositionPublisher_.publish(createFloat64MultiArrayFromVector(
    cmd_body_pos));

  contact_flag_t cmd_state_leg = modeNumber2StanceLeg(mpcMrtInterface_->getReferenceManager().
        getModeSchedule().modeAtTime(currentObservation_.time));
  contact_flag_t est_state_leg = modeNumber2StanceLeg(currentObservation_.mode);

  vector_t cmd_contact_state(4);
  for (int i = 0; i < 4; i++)
  {
    cmd_contact_state(i) = cmd_state_leg[i] ? 0 : 1;
  }
  cmdContactStatePublisher_.publish(createFloat64MultiArrayFromVector(cmd_contact_state));

  // body angular vel(measured
  vPino_.segment<3>(3) = getEulerAnglesZyxDerivativesFromGlobalAngularVelocity<scalar_t>(
      measuredRbdState_.head<3>(),
      measuredRbdState_.segment<3>(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum));
  // body linear vel
  vPino_.head(3) = measuredRbdState_.segment<3>(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum + 3); 
  // joint vel
  vPino_.tail(12) = measuredRbdState_.segment(6 + leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum, 
                                              leggedInterface_->getCentroidalModelInfo().actuatedDofNum);  
  // body and joint pos
  qPino_ = currentObservation_.state.segment<6+12>(6);

  //RL body estimate
  vPino_RL_.segment<3>(3) = getEulerAnglesZyxDerivativesFromGlobalAngularVelocity<scalar_t>(
      measuredRbdStateRL_.head<3>(),
      measuredRbdStateRL_.segment<3>(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum));
  // body linear vel
  vPino_RL_.head(3) = measuredRbdStateRL_.segment<3>(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum + 3); 
  // joint vel
  vPino_RL_.tail(12) = measuredRbdStateRL_.segment(6 + leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum, 
                                              leggedInterface_->getCentroidalModelInfo().actuatedDofNum);  
  // body and joint pos
  qPino_RL_ = currentObservation_RL_.state.segment<6+12>(6);

  auto est_feet_pos_vel = computeFeetPosAndVel(qPino_, vPino_);
  estFeetPositionPublisher_.publish(createFloat64MultiArrayFromVector(est_feet_pos_vel[0]));
  estFeetVelocityPublisher_.publish(createFloat64MultiArrayFromVector(est_feet_pos_vel[1]));
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->
    getSwingTrajectoryPlanner()->setCurrentFeetPosition(est_feet_pos_vel[0]);

  auto est_RLfeet_pos_vel = computeFeetPosAndVel(qPino_RL_, vPino_RL_);
  estRLFeetPositionPublisher_.publish(createFloat64MultiArrayFromVector(est_RLfeet_pos_vel[0]));
  estRLFeetVelocityPublisher_.publish(createFloat64MultiArrayFromVector(est_RLfeet_pos_vel[1]));
  // leggedInterface_->getSwitchedModelReferenceManagerPtr()->
  //   getSwingTrajectoryPlanner()->setCurrentFeetPosition(est_RLfeet_pos_vel[0]);
    
  Eigen::Matrix3d RotMatBody = getRotationMatrixFromZyxEulerAngles<double>(measuredRbdState_.segment<3>(0));
  Eigen::Vector3d velLocal = RotMatBody.transpose() * vPino_.head(3);
  velLocal[2] = vPino_[3];  // dyaw
  // std::cout<<" velLocal:"<<velLocal.transpose()<<std::endl;
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setEstVelLocal(velLocal);  // add by henning on 2023-12-6
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setRotMat(RotMatBody);

  if(controller_switch_flag){

    auto angVelBody_ = getEulerAnglesZyxDerivativesFromGlobalAngularVelocity<scalar_t>(
      measuredRbdStateRL_.head<3>(),
      measuredRbdStateRL_.segment<3>(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum));
    auto lineVelBody_ = measuredRbdStateRL_.segment<3>(leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum + 3); 
    Eigen::Matrix3d RotMatBodyRl = getRotationMatrixFromZyxEulerAngles<double>(measuredRbdStateRL_.segment<3>(0));
    // std::cout<<"lineVelBody_:"<<lineVelBody_.transpose()<<std::endl;  //world
    Eigen::Vector3d velLocalRl = RotMatBodyRl.transpose() * lineVelBody_;  //local
    velLocalRl[2] = angVelBody_[0];
    setEstVelLocalRl(velLocalRl);
    // setRotMatRl(RotMatBodyRl);   //这里只需要local系下的速度，rot(local -> world)
    if(isPassiveTrot_.load()){
      rlPassiveMode();
    }
    // std::cout<<"RotMatBodyRl:"<<RotMatBodyRl<<std::endl;
    // std::cout<<" velLocalRl:"<<velLocalRl.transpose()<<std::endl;
  }

  // update latest feet stance position
  // for (int i = 0; i < 4; i++)
  // {
  //   stateEstimate_->getLatestStancePos()[i] = 
  //       cmd_state_leg[i] ? est_feet_pos_vel[0].segment<3>(3*i) : stateEstimate_->getLatestStancePos()[i];
  // }
  // add by fzy: record bodyVel
  vector_t bodyGlobalVel_ = stateEstimate_->getBodyVel();
  // bodyGlobalVel_.head(3) = vPino_.head(3);
  bodyGobalVelPublisher_.publish(createFloat64MultiArrayFromVector(bodyGlobalVel_));//xyz,angxyz
  vector_t bodyLocalVel_ = vector_t::Zero(6);
  Eigen::Vector3d tmpBodyLoalVel = RotMatBody.transpose() * vPino_.head(3);  //vPion_.head(3) -> global
  bodyLocalVel_.head(3) = tmpBodyLoalVel;
  bodyLocalVel_.tail(3) = vPino_.segment<3>(3); //vPino_.segment<3>(3) -> local
  bodyLocalVelPublisher_.publish(createFloat64MultiArrayFromVector(bodyLocalVel_));//xyz,angxyz
  scalar_t recordDt_ = time.toSec() - recordDataTime.toSec();
  if(dataRecordFlag_&&recordDt_>0.02){
  // if(dataRecordFlag_){
    recordDataTime = time;
    vector_t recordJointPos_ = jointPos_;
    vector_t recordJointVel_ = jointVel_;
     

    recordJointPos_.segment<3>(0) = jointPos_.segment<3>(6); //fr
    recordJointPos_.segment<3>(3) = jointPos_.segment<3>(0);  //fl
    recordJointPos_.segment<3>(6) = jointPos_.segment<3>(9);  //rr
    recordJointPos_.segment<3>(9) = jointPos_.segment<3>(3);  //rl
    
    recordJointVel_.segment<3>(0) = jointVel_.segment<3>(6);
    recordJointVel_.segment<3>(3) = jointVel_.segment<3>(0);
    recordJointVel_.segment<3>(6) = jointVel_.segment<3>(9);
    recordJointVel_.segment<3>(9) = jointVel_.segment<3>(3);
    ofs_amp<<"[";
    for(int i=0;i<12;i++)
      ofs_amp<<recordJointPos_(i)<<", ";
    for(int i=0;i<12;i++)  
      ofs_amp<<recordJointVel_(i)<<", ";
    for(int i=0;i<3;i++)
      ofs_amp<<bodyLocalVel_(i)<<", ";
    for(int i=5;i>=3;i--)
      ofs_amp<<bodyLocalVel_(i)<<", ";
    ofs_amp << qPino_(2)<<"],"<<std::endl;

  }

  // end fzy
  
  int num_contact_legs = 0;
  for (int i = 0; i < 4; i++) {
    if (estContactFlag[i]) {
      num_contact_legs++;
      latest_stance_position_[i] = est_feet_pos_vel[0].segment<3>(3*i);
    }
    W_pla_(i, 1) = latest_stance_position_[i](0);
    W_pla_(i, 2) = latest_stance_position_[i](1);
  }

  Eigen::Vector4d W_zf = Eigen::Vector4d::Zero();
  W_zf << latest_stance_position_[0][2], latest_stance_position_[1][2], latest_stance_position_[2][2], latest_stance_position_[3][2];
  Eigen::MatrixXd W_pla_inv_;
  henning::pseudoInverse2(W_pla_, 0.001, W_pla_inv_);

  double filter_alpha = 0.4;

  Eigen::Vector3d plane_est = W_pla_inv_ * W_zf * filter_alpha + (1 - filter_alpha) * plane_est_prev_;

  std::array<double, 12> est_foot_pos_viz;
  for (int i = 0; i < 4; i++){
    est_foot_pos_viz[i * 3] = latest_stance_position_[i][0];
    est_foot_pos_viz[i * 3 + 1] = latest_stance_position_[i][1];
    est_foot_pos_viz[i * 3 + 2] = plane_est[0] + plane_est[1] * est_foot_pos_viz[i * 3] + plane_est[2] * est_foot_pos_viz[i * 3 + 1];
  }
  vector_t plane_est_msg_to_rfm = vector_t::Zero(8);
  plane_est_msg_to_rfm[2] = plane_est[0];
  plane_est_msg_to_rfm[3] = plane_est[1];
  plane_est_msg_to_rfm[4] = plane_est[2];
  plane_est_msg_to_rfm[5] = measuredRbdState_[0];
  plane_est_msg_to_rfm[6] = measuredRbdState_[1];
  plane_est_msg_to_rfm[7] = measuredRbdState_[2];

  plane_est_msg_.data[2] = plane_est[0]; // 顺序不要修改，不要在修改plane_est后再赋值
  plane_est_msg_.data[3] = plane_est[1];
  plane_est_msg_.data[4] = plane_est[2];
  plane_est_msg_.data[5] = measuredRbdState_[0];  // zyx
  plane_est_msg_.data[6] = measuredRbdState_[1];
  plane_est_msg_.data[7] = measuredRbdState_[2];

  plane_est_prev_ = plane_est;
  plane_est << -plane_est(1), -plane_est(2), 1;
  plane_est.normalize(); // 单位化
  Eigen::Matrix3d rotMat = Eigen::Matrix3d::Identity();
  Eigen::MatrixXd rotMat_inv;

  rotMat << cos(measuredRbdState_[0]), sin(measuredRbdState_[0]), 0, 
            sin(measuredRbdState_[0]), -cos(measuredRbdState_[0]), 0,
            0, 0, 1;                                                  // rbdState_[0]是yaw
  henning::pseudoInverse2(rotMat, 0.001, rotMat_inv);

  Eigen::Vector3d yibu = rotMat_inv * plane_est;

  double est_roll = asin(yibu[1]);
  double est_pitch = atan(yibu[0] / yibu[2]);

  // std::cout << "plane_est: " << plane_est  << std::endl;
  // std::cout << "est_roll: " << est_roll << ", est_pitch: " << est_pitch << std::endl;
  plane_est_msg_.data[0] = est_roll;
  plane_est_msg_.data[1] = est_pitch;
  plane_est_msg_to_rfm[0] = est_roll;
  plane_est_msg_to_rfm[1] = est_pitch;

  plane_est_pub.publish(plane_est_msg_);

  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setEstPlane(plane_est_msg_to_rfm);
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setAccZ(imuSensorHandle_.getLinearAcceleration()[2]);
  double velZ = measuredRbdState_[leggedInterface_->getCentroidalModelInfo().generalizedCoordinatesNum + 3 + 2];
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setVelZ(velZ);
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner()->setEstPlane(plane_est_msg_to_rfm);

  feet_array_t<vector3_t> feetPosInHipFrame;
  // Eigen::Matrix3d rotMatBody = getRotationMatrixFromZyxEulerAngles<double>(measuredRbdState_.segment<3>(0));

  for (int i = 0; i < 4; i++) {
    feetPosInHipFrame[i].setZero();
    if(estContactFlag[i]) {
      vector3_t leg_joint_pos = jointPos_.segment(i * 3, 3);
      feetPosInHipFrame[i] = singleLegKinematic_.forward(leg_joint_pos);
    }
  }
  // for (int i = 0; i < 4; i++) {
  //   feetPosInHipFrame[i] = rotMatBody.transpose() * (latest_stance_position_[i] - measuredRbdState_.segment<3>(3));
  // }
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setFootPosInHipFrame(feetPosInHipFrame);
  // leggedInterface_->getSwitchedModelReferenceManagerPtr()->setZVel(measuredRbdState_[23]);

  // Update the current state of the system
  mpcMrtInterface_->setCurrentObservation(currentObservation_);

  // Load the latest MPC policy
  mpcMrtInterface_->updatePolicy();

  // Evaluate the current policy
  vector_t optimizedState, optimizedInput;
  size_t plannedMode = 0;  // The mode that is active at the time the policy is evaluated at.
  mpcMrtInterface_->evaluatePolicy(currentObservation_.time, currentObservation_.state, optimizedState, optimizedInput, plannedMode);
 
  // add for safety check and measure
  if((currentObservation_.time > mpcMrtInterface_->getPolicy().timeTrajectory_.back()))
  {
    setControllerStop();
    // setControllerPassive(time);
  }

  // add for contactForce check and filter
  controlTimer_.endTimer();

  // if(controlTimer_.getLastIntervalInMilliseconds()/1000.0 <1)
  // {
  //   MpcContactForce_ = MpcContactForce_ * (1-0.001) +  optimizedInput.head(12)*0.001;
  //   optimizedInput.head(12) = MpcContactForce_;
  //   // std::cout<<" MpcContactForce_:"<<MpcContactForce_<<std::endl;
  // }

  bool checkContactForce_ = checkMpcContactForceErr(time,optimizedInput.head(12),leggedInterface_->getSwitchedModelReferenceManagerPtr()->getGaitType());
  if(SAFETY_CHECK&&checkContactForce_)
  {
    setControllerPassive(time);
    return ;
  }
  // add for contactForce check and filter
  auto stairModeEnable_ = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner()->getStairMode();
  lifted_detection_enable = stairModeEnable_? false:true;

  contact_flag_t cmdContactFlag = modeNumber2StanceLeg(mpcMrtInterface_->getReferenceManager().getModeSchedule().modeAtTime(currentObservation_.time));

  // if(false){
  bool noDesCmd = (ros::Time::now() - cmdTime) > ros::Duration(0.2);
  if(controller_switch_flag && !isPassiveTrot_.load() && noDesCmd){
    checkLiftedRL = checkLiftedinRL(time, est_feet_pos_vel[0]);
  }else {
    checkLifted = checkRobotLifted(time, est_feet_pos_vel[0], cmdContactFlag);
  }
  auto cmdVelAbs_ = std::sqrt(command_.x*command_.x + command_.y*command_.y+ command_.yaw*command_.yaw);
  if (checkLifted) {
  // if (false) {
    onEnterLiftedMode(time);
  }
  onExitLiftedMode(time);
  // }  
  
    //for RL controller running added by WangC on 2024/10/08
  if(controller_switch_flag){
    enterLiftedTime = time;
    // mpcRunning_ = false;
    // runningLiftedMode = true;
    // runningPositionMode = true;

    start_control = true;

    if(checkLiftedRL && (mode_ == Mode::WALK || mode_ == Mode::UPSTAIR)&&!isPassiveTrot_.load()){
      mode_ = Mode::STAND;
    }

    // if ( mode_ == Mode::WALK && fabs(command_.x) < 0.01 && fabs(command_.y) < 0.01 && fabs(command_.yaw) < 0.01 && vPino_.norm() < 0.1)
    // {
    //   ROS_INFO_STREAM("no command! switched to FREEZE mode");
    //   mode_ = Mode::FREEZE;
    //   cmd_move_flag = false;
    // }else if (mode_ == Mode::FREEZE && (fabs(command_.x) > 0.01 || fabs(command_.y) > 0.01 || fabs(command_.yaw) > 0.01))
    // {
    //   ROS_INFO_STREAM("received command! switched to WALK mode");
    //   mode_ = Mode::WALK;
    //   cmd_move_flag = true;
    // }



    switch (mode_)
    {
    case Mode::DEFAULT:
      handleDefautMode();
      break;
    case Mode::LIE:
      handleLieMode();
      break;
    case Mode::STAND:
      handleStandMode();
      break;
    case Mode::WALK:
      handleWalkMode();  //强化学习 walk
      if (walkCount_ > 3)
        exit(0);
      break;
    case Mode::UPSTAIR:
      handleWalkMode(); 
      if (walkCount_ > 3)
        exit(0);
      break;
    case Mode::FREEZE:
      handleFreezeMode();
      break;
    //// add rl_stand mode
    case Mode::RLSTAND:  //强化学习站立
      handleRlstandMode();
      break;

    default:
      ROS_ERROR_STREAM("Unexpected mode encountered: " << static_cast<int>(mode_));
      break;
    }

    vector_t output_torque(joint_dim_);
    for (int j = 0; j < hybridJointHandles_.size(); j++)
    {
      pos_des_output_(j) = hybridJointHandles_[j].getPositionDesired();
      vel_des_output_(j) = hybridJointHandles_[j].getVelocityDesired();
      output_torque(j) = hybridJointHandles_[j].getFeedforward() +
                         hybridJointHandles_[j].getKp() * (hybridJointHandles_[j].getPositionDesired() - hybridJointHandles_[j].getPosition()) +
                         hybridJointHandles_[j].getKd() * (hybridJointHandles_[j].getVelocityDesired() - hybridJointHandles_[j].getVelocity());

      if(output_torque(j) > 100.0) output_torque(j) = 100.0;
      else if(output_torque(j) < -100.0) output_torque(j) = -100.0;
    }

    rlComputePosPublisher_.publish(createFloat64MultiArrayFromVector(pos_des_output_));
    rlComputeTauPublisher_.publish(createFloat64MultiArrayFromVector(output_torque));

    loopCount_++;

  }
  // else{
  //   mpcRunning_ = true;
  //   // runningLiftedMode = false;
  //   // runningPositionMode = false;
  // }
  //for RL controller running added by WangC on 2024/10/08

  // add rosparm LiftedMode for leggedTopic control robotPosXyz
  if(runningLiftedMode)
    ros::param::set("/robotState/liftedMode","doing");
  else
    ros::param::set("/robotState/liftedMode","done");

  if(runningTwoLegStandMode) {
    switch (twoLegStandStage_) {
    case 0:
      runTwoLegStandAdjustStage(time);
      break;
    case 1:
      runTwoLegStandPushStage(time);
      break;
    case 2:
      runTwoLegStandFinalStage(time);
      break;
    case -1:
      runTwoLegStandRecoverStage(time);
      break;
    default:
      break;
    }
  }

  if(runningBackFlipMode) {
    runBackFlipMode(time);
  }

  mpcPlannedBodyPositionPublisher_.publish(createFloat64MultiArrayFromVector(
    optimizedState.segment(6, 6)));
  mpcPlannedContactForcePublisher_.publish(createFloat64MultiArrayFromVector(
    optimizedInput.head(12)));
  
  vPino_.tail(12) = optimizedInput.tail(12);  // joint vel
  qPino_ = optimizedState.segment<6+12>(6);  // body and joint pos
  auto mpc_planned_feet_pos_vel = computeFeetPosAndVel(qPino_, vPino_);
  mpcPlannedFeetPositionPublisher_.publish(createFloat64MultiArrayFromVector(mpc_planned_feet_pos_vel[0]));
  mpcPlannedFeetVelocityPublisher_.publish(createFloat64MultiArrayFromVector(mpc_planned_feet_pos_vel[1]));
  mpcPlannedJointPosPublisher_.publish(createFloat64MultiArrayFromVector(optimizedState.tail(12)));
  mpcPlannedJointVelPublisher_.publish(createFloat64MultiArrayFromVector(optimizedInput.tail(12)));
 
  // Whole body control
// for (int i = 0; i < 4; i++){
  //   if (estContactFlag[i]){
  //     optimizedInput(i * 3) += -9.81 * leggedInterface_->getCentroidalModelInfo().robotMass * sin(est_pitch) / num_contact_legs;
  //   }
  // }

  // std::cout << "robotMass : " << leggedInterface_->getCentroidalModelInfo().robotMass << std::endl; //robotMass : 18.8846

  
  currentObservation_.input = optimizedInput;
  auto gaitTy_ = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getGaitType();
  auto gaitLevel_ = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getGaitLevel();


  wbcTimer_.startTimer();
  wbc_->setCmdBodyPosVel(cmd_body_pos, cmd_body_vel);
  const auto& early_late_contact = stateEstimate_->getEarlyLateContact();
  wbc_->setEarlyLateContact(early_late_contact);

  vector_t posDes = centroidal_model::getJointAngles(optimizedState, leggedInterface_->getCentroidalModelInfo());
  vector_t velDes = centroidal_model::getJointVelocities(optimizedInput, leggedInterface_->getCentroidalModelInfo());

  scalar_t dt = period.toSec();

  if(!controller_switch_flag) {
    vector_t x = wbc_->update(optimizedState, optimizedInput, measuredRbdState_, plannedMode, gaitTy_, plane_est, period.toSec());
    wbcTimer_.endTimer();

    vector_t torque = x.tail(12);
    tau_des_filtered_ = torque; 

    posDes = posDes + 0.5 * x.segment(6, 12) * dt * dt;
    velDes = velDes + x.segment(6, 12) * dt;

    wbcPlannedBodyAccelerationPublisher_.publish(createFloat64MultiArrayFromVector(
      x.head(6)));
    wbcPlannedJointAccPublisher_.publish(createFloat64MultiArrayFromVector(
      x.segment(6, 12)));
    wbcPlannedContactForcePublisher_.publish(createFloat64MultiArrayFromVector(
      x.segment(18, 12)));
    wbcPlannedTorquePublisher_.publish(createFloat64MultiArrayFromVector(tau_des_filtered_));
  }

  wbcPlannedJointPosPublisher_.publish(createFloat64MultiArrayFromVector(posDes));
  wbcPlannedJointVelPublisher_.publish(createFloat64MultiArrayFromVector(velDes));

  const scalar_t filter_factor = 1.0;
  pos_des_filtered_ = (1 - filter_factor) * pos_des_filtered_ + filter_factor * posDes;
  vel_des_filtered_ = (1 - filter_factor) * vel_des_filtered_ + filter_factor * velDes;
  // tau_des_filtered_ = torque; 

  // add for safety check and measure
  if(SAFETY_CHECK)
    limitJointPosAndTor(pos_des_filtered_,tau_des_filtered_);

  filteredPlannedJointPosPublisher_.publish(createFloat64MultiArrayFromVector(pos_des_filtered_));
  filteredPlannedJointVelPublisher_.publish(createFloat64MultiArrayFromVector(vel_des_filtered_));



  // Safety check, if failed, stop the controller
  // add for safety check tor
  bool checkJointTor_ = checkJointTorErr(time,tau_des_filtered_);
  bool checkWbcQp_ = checkWbcQpSolvedErr(time);

  if (SAFETY_CHECK&&checkJointTor_) 
  {
    setControllerStop();
  }

  if(!runningPositionMode) { // stop checking orientation for position control mode
    if (!safetyChecker_->check(currentObservation_, optimizedState, optimizedInput)||checkWbcQp_) {
      // stopRequest(time);
      // setControllerStop();
      setControllerPassive(time);
      ROS_ERROR_STREAM("[Legged Controller] Safety check failed, stopping the controller.");

    }
  }
  // add for roll-recovery
  if(enterPassiveModel_){
    passiveModelTimer_.endTimer();
    // if(rollRecoveryMode&&passiveModelTimer_.getLastIntervalInMilliseconds()/1000.0>3){
    if(rollRecoveryMode){
      scalar_t rollLast = currentObservation_.state(11);

      if((rollLast<M_PI_4&&rollLast>-M_PI_4)&&curRecoveryState==0){
        ROS_INFO_STREAM("!!!!!!!Finished Body Recovery!!!!!!!");
        rollRecoveryMode = false;
        starting(ros::Time::now());
        // for roll-recvery
        enterPassiveModel_ = false;
        runningPositionMode = false;
        runningLiftedMode = false;
        runningTwoLegStandMode = false;
        runningTransMode = false;
        runningBackFlipMode = false;
        controller_switch_flag = false; //mpc
        curRotState = "getdown";
        curControState = "on";
        ros::param::set("robotBodyRecoveryState","true");
        // 设置robotStateCallBack允许getdown控制　load_controller_flag = true;
        robotStateCallBack = true;

      }
      else
        bodyAdjustmentAndRecovery(currentObservation_, ros::Time::now());
    }
  } else{
    rollRecoveryMode = false;
  }


  // add for pawup Leg gravityCompensation
  vector_t pawupLegTor_;
  static bool start_pawup = false;
  // auto gaitTy_ = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getGaitType();
  auto swingLegGraComFlag = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner()->getSwingLegGraCompensationFlag();


  if(gaitTy_==8||gaitTy_==9){

    vector_t tor = wbc_->fixedBaseGravityCompensation(optimizedState, optimizedInput, measuredRbdState_, plannedMode, period.toSec());
    pawupLegTor_ = tor.tail(12);
  }
  else{
    start_pawup = false;
  }

  vector_t output_torque(12);
  // std::cout<<" rollRecoveryMode:"<<rollRecoveryMode<<" curRecoveryState:"<<curRecoveryState<<std::endl;

  //Set Joint Command
  for (size_t j = 0; j < leggedInterface_->getCentroidalModelInfo().actuatedDofNum; ++j) {
    int leg = 0;
    if (j >= 0 && j < 3)
      leg = 0;
    else if (j >= 3 && j < 6)
      leg = 2;
    else if (j >= 6 && j < 9)
      leg = 1;
    else if (j >= 9 && j < 12)
      leg = 3;
      
    // for gravity compensation test
    if (gravity_compensation_flag){
      // if (leg == 0)
      //   hybridJointHandles_[j].setCommand(pos_des_filtered_(j), vel_des_filtered_(j), 0, 0, 0);
      // else
        hybridJointHandles_[j].setCommand(pos_des_filtered_(j), vel_des_filtered_(j), 0, 0, tau_des_filtered_(j));
    }
    else if(rollRecoveryMode){
      // std::cout<<" rollRecoveryMode:"<<rollRecoveryMode<<" curRecoveryState:"<<curRecoveryState<<std::endl;
      // std::cout<<" cur time:"<<time.toSec()<<" start time:"<<recoveryStateStartTime.toSec()<<std::endl;
      double phase_recovery = (time.toSec() - recoveryStateStartTime.toSec()) >= recoveryTime_ ?
                           1.0 : (time.toSec() - recoveryStateStartTime.toSec()) / recoveryTime_;
      double desJointPosRecovery = recoveryCurPos[j] * (1-phase_recovery) + recoveryTargetPos[j] * phase_recovery;
      hybridJointHandles_[j].setCommand(desJointPosRecovery, 0, kp_recovery_mode_, kd_recovery_mode_, 0);
      // std::cout<<" j:"<<j<<" "<<recoveryTargetPos[j]<<std::endl;
      //   std::cout<<" "<<kp_recovery_mode_<<" "<<kd_recovery_mode_<<std::endl;
      // if(j==4){
      //   std::cout<<" phase_recovery:"<<phase_recovery<<std::endl;
      // }

      if(phase_recovery >= 1.0){
        recoveryCount_ = 0;
      }

    }
    else if(runningTransMode){
      // time1
      // double phase_transpos = time.toSec() - enterTransModeTime.toSec() >= 1.5 ? 1 : (time.toSec() - enterTransModeTime.toSec()) / 1.5;
      // auto curTime = ros::Time::now();
      // phase_transpos = time.toSec() - enterTransModeTime.toSec() >= 1.5 ? 1 : (time.toSec() - enterTransModeTime.toSec()) / 1.5;
      // double timeElapsed = time.toSec() - enterTransModeTime.toSec();

      // time2
      auto curTime = std::chrono::high_resolution_clock::now();
      std::chrono::duration<double> timeElapsed = curTime - enterTransModeTime;
      phase_transpos = timeElapsed.count() >= 1.5 ? 1 : (timeElapsed.count()) / 1.5;
      // double phase_transpos = timeElapsed.count() >= 3.5 ? 1 : (timeElapsed.count()) / 3.5;

      // double gait_duration = 0.48;
      // int cycle = (int)std::ceil(timeElapsed.count() / gait_duration); //第几个周期
      // double phase_cyc = fmod(timeElapsed.count(), gait_duration); //取余

      // std::cout<<" phase_transpos:"<<phase_transpos<<std::endl;

      double desJointPosTranposFinal = enterTransModeJointPos_[j] + (desTransModeJointPos_[j] - enterTransModeJointPos_[j]) * phase_transpos;
      if(phase_transpos >=1 && curRotState == "handshake_left_mode" && j == 2){

        double desJointPosTranposFinal = desTransModeJointPos_[j] + 0.3 * sin((timeElapsed.count() - 1.5)*3);
        // double desJointPosTranposFinal = desTransModeJointPos_[j] + 0.3 * sin((timeElapsed - 1.5));

        hybridJointHandles_[j].setCommand(desJointPosTranposFinal, 0, kp_lifted, kd_lifted, 0);

      }
      else if(phase_transpos >=1 && curRotState == "handshake_right_mode" && j == 8){
        double desJointPosTranposFinal = desTransModeJointPos_[j] + 0.3 * sin((timeElapsed.count() - 1.5)*3);
        // double desJointPosTranposFinal = desTransModeJointPos_[j] + 0.3 * sin((timeElapsed - 1.5));

        hybridJointHandles_[j].setCommand(desJointPosTranposFinal, 0, kp_lifted, kd_lifted, 0);
        // hybridJointHandles_[j].setCommand(desJointPosTranposFinal, 0, 0, 0.2, 0);

      }
      else if(phase_transpos >=1 && curRotState == "getdown")
        hybridJointHandles_[j].setCommand(desJointPosTranposFinal, 0, 0, 3, 0);
      else 
        hybridJointHandles_[j].setCommand(desJointPosTranposFinal, 0, kp_lifted, kd_lifted, 0);  
      // std::cout<<" kp_lifted:"<<kp_lifted<<" kd_lifted:"<<kd_lifted<<std::endl;
      // std::cout<<desTransModeJointPos_<<std::endl;

      // for topic control use
      // std::cout<<" phase_transpos:"<<phase_transpos<<std::endl;

      if(!transFinished&&phase_transpos >= 1){
        transFinished = true;
        if(curRotState != "standup")
          ros::param::set("gsmp_trans_model_state","done");  
        else
          switchTostandupMpcControl();
      }
      // else
      //   ros::param::set("gsmp_trans_model_state","doing");
    }
    else if(runningLiftedMode) {
      double phase_lifted = time.toSec() - enterLiftedTime.toSec() >= pos_transition_time_lifted ? 1 : (time.toSec() - enterLiftedTime.toSec()) / pos_transition_time_lifted;
      double desJointPosLiftedFinal = enterLiftedJointPos_[j] + (cmdLiftedJointPos_[j] - enterLiftedJointPos_[j]) * phase_lifted;
      hybridJointHandles_[j].setCommand(desJointPosLiftedFinal, 0, kp_lifted, kd_lifted, 0);
    } else if (runningTwoLegStandMode) {
      hybridJointHandles_[j].setCommand(cmdTwoLegStandModeJointPos_[j], 0, cmdTwoLegStandModeKp_[j], cmdTwoLegStandModeKd_[j], cmdTwoLegStandModeTau_[j]);
      // std::cout<<j<<" cmdTwoLegStandModeKp_:"<<cmdTwoLegStandModeKp_[j]<<" kd:"<<cmdTwoLegStandModeKd_[j]<<std::endl;
    } else if (runningBackFlipMode) {
      hybridJointHandles_[j].setCommand(cmdBackFlipModeJointPos_[j], cmdBackFlipModeJointVel_[j], cmdBackFlipModeKp_[j], cmdBackFlipModeKd_[j], cmdBackFlipModeTau_[j]);
    }
    else if(runningPositionMode) {
      double phase_pos_mode = time.toSec() - enterPosModeTime.toSec() >= pos_transition_time_ ? 1 : (time.toSec() - enterPosModeTime.toSec()) / pos_transition_time_;
      double desJointPosFinal = enterPosModeJointPos_[j] + (cmdPosModeJointPos_[j] - enterPosModeJointPos_[j]) * phase_pos_mode;

      hybridJointHandles_[j].setCommand(desJointPosFinal, 0, kp_pos_mode_, kd_pos_mode_, 0);
    }
    else if(!controller_switch_flag){
      if (cmd_state_leg[leg])
      {
        hybridJointHandles_[j].setCommand(pos_des_filtered_(j), vel_des_filtered_(j), kp_stance, kd_stance, SWING_TEST ? 0.0 : tau_des_filtered_(j));
      }
      else
      {
        // std::cout<<" gait type:"<<curGaitType_<<std::endl;
        if(gaitTy_== 8 || gaitTy_== 9){
          if(swingLegGraComFlag)
            hybridJointHandles_[j].setCommand(pos_des_filtered_(j), vel_des_filtered_(j), 0, 0, tau_des_filtered_(j));
          else
            hybridJointHandles_[j].setCommand(pos_des_filtered_(j), vel_des_filtered_(j), kp_swing, kd_swing, tau_des_filtered_(j));
        }
        else
          hybridJointHandles_[j].setCommand(pos_des_filtered_(j), vel_des_filtered_(j), kp_swing, kd_swing, tau_des_filtered_(j));

      }
      
      // if (early_late_contact[0][leg])
      //   hybridJointHandles_[j].setCommand(pos_des_filtered_(j), vel_des_filtered_(j), 0, 0, torque(j));

      output_torque(j) = hybridJointHandles_[j].getFeedforward() + 
        hybridJointHandles_[j].getKp() * (hybridJointHandles_[j].getPositionDesired() - hybridJointHandles_[j].getPosition()) +
        hybridJointHandles_[j].getKd() * (hybridJointHandles_[j].getVelocityDesired() - hybridJointHandles_[j].getVelocity());

      if(output_torque(j) > 100.0) output_torque(j) = 100.0;
      else if(output_torque(j) < -100.0) output_torque(j) = -100.0;
    }
      if (!load_controller_flag&&!rollRecoveryMode) {
        hybridJointHandles_[j].setCommand(0, 0, 0, 3, 0);
      }
      else if (load_controller_flag && controller_msg_count == 0){
        ROS_INFO_STREAM("Controller connected successfully!");
        controller_msg_count++;
        // controlTimer_.startTimer();
        starting(time);

      }
  }
  if (emergency_flag) {
     ROS_ERROR_STREAM("[Legged Controller] Emergency!");  
     stopRequest(time);
  }

  outputTorquePublisher_.publish(createFloat64MultiArrayFromVector(output_torque));

  // Visualization
  // if(controller_switch_flag){
  //   std::cout<<" 1"<<std::endl;
  //   robotVisualizer_->update(currentObservation_RL_, mpcMrtInterface_->getPolicy(), mpcMrtInterface_->getCommand(), 
  //                         leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner(), est_foot_pos_viz);
  // }
  // else{

    robotVisualizer_->update(currentObservation_, mpcMrtInterface_->getPolicy(), mpcMrtInterface_->getCommand(), 
                          leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner(), est_foot_pos_viz);
  // }
  selfCollisionVisualization_->update(currentObservation_);

  // Publish the observation. Only needed for the command interface
  observationPublisher_.publish(ros_msg_conversions::createObservationMsg(currentObservation_));
}

void LeggedController::updateStateEstimation(const ros::Time& time, const ros::Duration& period) {
  // vector_t jointPos(hybridJointHandles_.size()), jointVel(hybridJointHandles_.size());
  contact_flag_t contacts;
  Eigen::Quaternion<scalar_t> quat;
  // contact_flag_t realContactFlag;
  vector3_t angularVel, linearAccel;
  matrix3_t orientationCovariance, angularVelCovariance, linearAccelCovariance;

// lf,lh,rf,rh
  for (size_t i = 0; i < hybridJointHandles_.size(); ++i) {
    jointPos_(i) = hybridJointHandles_[i].getPosition();
    jointVel_(i) = hybridJointHandles_[i].getVelocity();
    jointTor_(i) = hybridJointHandles_[i].getEffort();
  }
  
  contact_flag_t cmdContactFlag = modeNumber2StanceLeg(mpcMrtInterface_->getReferenceManager().
      getModeSchedule().modeAtTime(currentObservation_.time));
  stateEstimate_->updateCmdContact(cmdContactFlag);
  stateEstimate_->setStartStopTime4Legs(
    leggedInterface_->getSwitchedModelReferenceManagerPtr()->
      getSwingTrajectoryPlanner()->threadSaftyGetStartStopTime(currentObservation_.time));
  estContactFlag = stateEstimate_->estContactState(currentObservation_.time);
  for (size_t i = 0; i < contacts.size(); ++i) {
    realContactFlag[i] = contactHandles_[i].isContact();
    // estContactFlag[i] = contactHandles_[i].isContact();
  }  
  vector_t real_contact_state(4);
  vector_t est_contact_state(4);
  for (int i = 0; i < 4; i++)
  {
    real_contact_state(i) = realContactFlag[i] ? 0 : 1;
    est_contact_state(i) = estContactFlag[i] ? 0 : 1;
  }
  realContactStatePublisher_.publish(createFloat64MultiArrayFromVector(real_contact_state));
  estContactStatePublisher_.publish(createFloat64MultiArrayFromVector(est_contact_state));
  
  for (size_t i = 0; i < 4; ++i) {
    quat.coeffs()(i) = imuSensorHandle_.getOrientation()[i];
  }
  for (size_t i = 0; i < 3; ++i) {
    angularVel(i) = imuSensorHandle_.getAngularVelocity()[i];
    linearAccel(i) = imuSensorHandle_.getLinearAcceleration()[i];
  }
  //// average of acc on xy direction
  acc_mean = (std::abs(linearAccel(0)) + std::abs(linearAccel(1))) / 2;

  for (size_t i = 0; i < 9; ++i) {
    orientationCovariance(i) = imuSensorHandle_.getOrientationCovariance()[i];
    angularVelCovariance(i) = imuSensorHandle_.getAngularVelocityCovariance()[i];
    linearAccelCovariance(i) = imuSensorHandle_.getLinearAccelerationCovariance()[i];
  }

  //for RL controller state estimation added bu WangC
  propri_.jointPos = jointPos_;
  propri_.jointVel = jointVel_;
  propri_.baseAngVel = angularVel;

  propri_.jointPos.segment<3>(0) = jointPos_.segment<3>(0);
  propri_.jointPos.segment<3>(3) = jointPos_.segment<3>(6);
  propri_.jointPos.segment<3>(6) = jointPos_.segment<3>(3);
  propri_.jointPos.segment<3>(9) = jointPos_.segment<3>(9);
  
  propri_.jointVel.segment<3>(0) = jointVel_.segment<3>(0);
  propri_.jointVel.segment<3>(3) = jointVel_.segment<3>(6);
  propri_.jointVel.segment<3>(6) = jointVel_.segment<3>(3);
  propri_.jointVel.segment<3>(9) = jointVel_.segment<3>(9);

  vector3_t gravityVector(0, 0, -1);
  vector3_t zyx = quatToZyx(quat);
  matrix_t inverseRot = getRotationMatrixFromZyxEulerAngles(zyx).inverse();
  propri_.projectedGravity = inverseRot * gravityVector;
  //for RL controller state estimation added bu WangC

  realTorquePublisher_.publish(createFloat64MultiArrayFromVector(jointTor_));
  auto remove_gravity = linearAccel;
  remove_gravity(2) -= 9.81;
  imuMeasuredBodyAccelerationPublisher_.publish(createFloat64MultiArrayFromVector(remove_gravity));
  realJointPosPublisher_.publish(createFloat64MultiArrayFromVector(jointPos_));
  realJointVelPublisher_.publish(createFloat64MultiArrayFromVector(jointVel_));

  stateEstimate_->updateJointStates(jointPos_, jointVel_);
  stateEstimate_->updateContact(SWING_TEST ? cmdContactFlag : estContactFlag);
  stateEstimate_->updateImu(quat, angularVel, linearAccel, orientationCovariance, angularVelCovariance, linearAccelCovariance);
  measuredRbdState_ = stateEstimate_->update(time, period);

  if(!runningPositionMode && !controller_switch_flag && !enterPassiveModel_) {
    currentObservation_.time += period.toSec();
  }
  // std::cout<<"obs time:"<<currentObservation_.time<<std::endl;
  // currentObservation_.time = time.toSec();
  scalar_t yawLast = currentObservation_.state(9);
  currentObservation_.state = rbdConversions_->computeCentroidalStateFromRbdModel(measuredRbdState_);
  currentObservation_.state(9) = yawLast + angles::shortest_angular_distance(yawLast, currentObservation_.state(9));
  currentObservation_.mode = stateEstimate_->getMode();

  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setPitchRoll(
    stateEstimate_->getEstPitch(), 
    stateEstimate_->getEstRoll());
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner()->
    setPitchRoll(stateEstimate_->getEstPitch(), stateEstimate_->getEstRoll());

  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setFeetBias(stateEstimate_->getFeetBiasWorld());

  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setEstContactFlag(estContactFlag);
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner()->
    setBodyVel(stateEstimate_->getBodyVel());
  
  stateEstimate_->pubEstVel();

  stateEstimate_->setCmdTorque(jointTor_);
  stateEstimate_->estContactForce(period);  
  estContactForcePublisher_.publish(createFloat64MultiArrayFromVector(
    stateEstimate_->getEstContactForce()));
  estDisturbanceTorquePublisher_.publish(createFloat64MultiArrayFromVector(
    stateEstimate_->getEstDisturbanceTorque()));

  // early late contact detection
  auto modeScheduleCheck_ = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getModeScheduleThreadSafe();
  size_t numPhases_ = modeScheduleCheck_.modeSequence.size();
  // std::cout<<"numPhases_:"<<numPhases_<<std::endl;
  if(numPhases_>2){
    stateEstimate_->earlyLateContactDetection(
      leggedInterface_->getSwitchedModelReferenceManagerPtr()->getModeScheduleThreadSafe(),
      currentObservation_.time);
    
    leggedInterface_->getSwitchedModelReferenceManagerPtr()->setEarlyLateContact(stateEstimate_->getEarlyLateContact()); 
  }
}

  //for RL controller state estimation added by WangC
void LeggedController::updateStateEstimationForRL(const ros::Time& time, const ros::Duration& period) {
  // vector_t jointPos(hybridJointHandles_.size()), jointVel(hybridJointHandles_.size());
  contact_flag_t contacts;
  Eigen::Quaternion<scalar_t> quat;
  // contact_flag_t realContactFlag;
  vector3_t angularVel, linearAccel;
  matrix3_t orientationCovariance, angularVelCovariance, linearAccelCovariance;
  vector_t jointTorRL_,jointPosRL_, jointVelRL_;
  jointTorRL_ = vector_t::Zero(hybridJointHandles_.size());
  jointPosRL_ = vector_t::Zero(hybridJointHandles_.size());
  jointVelRL_ = vector_t::Zero(hybridJointHandles_.size());

// lf,lh,rf,rh
  for (size_t i = 0; i < hybridJointHandles_.size(); ++i) {
    jointPosRL_(i) = hybridJointHandles_[i].getPosition();
    jointVelRL_(i) = hybridJointHandles_[i].getVelocity();
    jointTorRL_(i) = hybridJointHandles_[i].getEffort(); 
  }
  
  contacts = stateEstimateRL_->estRLContactState(currentObservation_RL_.time);

  vector_t est_contact_state(4);
  for (int i = 0; i < 4; i++)
  {
    est_contact_state(i) = contacts[i] ? 0 : 1;
  }

  estContactStatePublisher_.publish(createFloat64MultiArrayFromVector(est_contact_state));

  for (size_t i = 0; i < 4; ++i) {
    quat.coeffs()(i) = imuSensorHandle_.getOrientation()[i];
  }
  for (size_t i = 0; i < 3; ++i) {
    angularVel(i) = imuSensorHandle_.getAngularVelocity()[i];
    linearAccel(i) = imuSensorHandle_.getLinearAcceleration()[i];
  }
  for (size_t i = 0; i < 9; ++i) {
    orientationCovariance(i) = imuSensorHandle_.getOrientationCovariance()[i];
    angularVelCovariance(i) = imuSensorHandle_.getAngularVelocityCovariance()[i];
    linearAccelCovariance(i) = imuSensorHandle_.getLinearAccelerationCovariance()[i];
  }

  propri_.jointPos = jointPosRL_;
  propri_.jointVel = jointVelRL_;
  propri_.baseAngVel = angularVel;

  propri_.jointPos.segment<3>(0) = jointPosRL_.segment<3>(0);
  propri_.jointPos.segment<3>(3) = jointPosRL_.segment<3>(6);
  propri_.jointPos.segment<3>(6) = jointPosRL_.segment<3>(3);
  propri_.jointPos.segment<3>(9) = jointPosRL_.segment<3>(9);
  
  propri_.jointVel.segment<3>(0) = jointVelRL_.segment<3>(0);
  propri_.jointVel.segment<3>(3) = jointVelRL_.segment<3>(6);
  propri_.jointVel.segment<3>(6) = jointVelRL_.segment<3>(3);
  propri_.jointVel.segment<3>(9) = jointVelRL_.segment<3>(9);

  vector3_t gravityVector(0, 0, -1);
  vector3_t zyx = quatToZyx(quat);
  matrix_t inverseRot = getRotationMatrixFromZyxEulerAngles(zyx).inverse();
  propri_.projectedGravity = inverseRot * gravityVector;

  realTorquePublisher_.publish(createFloat64MultiArrayFromVector(jointTorRL_));
  auto remove_gravity = linearAccel;
  remove_gravity(2) -= 9.81;
  imuMeasuredBodyAccelerationPublisher_.publish(createFloat64MultiArrayFromVector(remove_gravity));
  realJointPosPublisher_.publish(createFloat64MultiArrayFromVector(jointPosRL_));
  realJointVelPublisher_.publish(createFloat64MultiArrayFromVector(jointVelRL_));

  stateEstimateRL_->updateJointStates(jointPosRL_, jointVelRL_);
  stateEstimateRL_->updateContact(contacts);
  stateEstimateRL_->updateImu(quat, angularVel, linearAccel, orientationCovariance, angularVelCovariance, linearAccelCovariance);
  measuredRbdStateRL_ = stateEstimateRL_->update(time, period);

  currentObservation_RL_.time += period.toSec();
  // scalar_t yawLast = currentObservation_RL_.state(9);
  currentObservation_RL_.state = rbdConversions_->computeCentroidalStateFromRbdModel(measuredRbdStateRL_);
  // currentObservation_RL_.state(9) = yawLast + angles::shortest_angular_distance(yawLast, currentObservation_RL_.state(9));
  // currentObservation_RL_.mode = stateEstimateRL_->getMode();

  
  stateEstimateRL_->pubEstVel();

  stateEstimateRL_->setCmdTorque(jointTorRL_);
  stateEstimateRL_->estContactForce(period);  
  estContactForcePublisher_.publish(createFloat64MultiArrayFromVector(
    stateEstimateRL_->getEstContactForce()));
  estDisturbanceTorquePublisher_.publish(createFloat64MultiArrayFromVector(
    stateEstimateRL_->getEstDisturbanceTorque()));

}
  
LeggedController::~LeggedController() {
  controllerRunning_ = false;
  if (mpcThread_.joinable()) {
    mpcThread_.join();
  }
  std::cerr << "########################################################################";
  std::cerr << "\n### MPC Benchmarking";
  std::cerr << "\n###   Maximum : " << mpcTimer_.getMaxIntervalInMilliseconds() << "[ms].";
  std::cerr << "\n###   Average : " << mpcTimer_.getAverageInMilliseconds() << "[ms]." << std::endl;
  std::cerr << "########################################################################";
  std::cerr << "\n### WBC Benchmarking";
  std::cerr << "\n###   Maximum : " << wbcTimer_.getMaxIntervalInMilliseconds() << "[ms].";
  std::cerr << "\n###   Average : " << wbcTimer_.getAverageInMilliseconds() << "[ms].";

  delete dataReader_;
  delete backFlipCtrl_;
  delete []backFlipCmd_;
  delete []backFlipData_;
}

void LeggedController::setupMotorParam(const std::string& motorFile)
{
  scalar_t kpStance, kdStance, kpSwing, kdSwing, kpLifted, kdLifted;
  loadData::loadCppDataType(motorFile, "motor_param.kp_stance", kpStance);
  loadData::loadCppDataType(motorFile, "motor_param.kd_stance", kdStance);
  loadData::loadCppDataType(motorFile, "motor_param.kp_swing", kpSwing);
  loadData::loadCppDataType(motorFile, "motor_param.kd_swing", kdSwing);
  loadData::loadCppDataType(motorFile, "motor_param.kp_lifted", kpLifted);
  loadData::loadCppDataType(motorFile, "motor_param.kd_lifted", kdLifted);
  loadData::loadCppDataType(motorFile, "motor_param.kp_recovery", kpRecovery_);
  loadData::loadCppDataType(motorFile, "motor_param.kd_recovery", kdRecovery_);

  kp_stance = kpStance;
  kd_stance = kdStance;

  kp_swing = kpSwing;
  kd_swing = kdSwing;

  kp_lifted = kpLifted;
  kd_lifted = kdLifted;

  kp_pos_mode_ = kp_lifted;
  kd_pos_mode_ = kd_lifted;

  kp_recovery_mode_ = kpRecovery_;
  kd_recovery_mode_ = kdRecovery_;
}

void LeggedController::setupLeggedInterface(const std::string& taskFile, const std::string& urdfFile, const std::string& referenceFile,
                                            bool verbose) {
  leggedInterface_ = std::make_shared<LeggedInterface>(taskFile, urdfFile, referenceFile);
  leggedInterface_->setupOptimalControlProblem(taskFile, urdfFile, referenceFile, verbose);
}

void LeggedController::setupMpc() {
  mpc_ = std::make_shared<SqpMpc>(leggedInterface_->mpcSettings(), leggedInterface_->sqpSettings(),
                                  leggedInterface_->getOptimalControlProblem(), leggedInterface_->getInitializer());
  rbdConversions_ = std::make_shared<CentroidalModelRbdConversions>(leggedInterface_->getPinocchioInterface(),
                                                                    leggedInterface_->getCentroidalModelInfo());

  const std::string robotName = "legged_robot";
  ros::NodeHandle nh;
  // Gait receiver
  // auto gaitReceiverPtr =
  //     std::make_shared<GaitReceiver>(nh, leggedInterface_->getSwitchedModelReferenceManagerPtr()->getGaitSchedule(), robotName);
  // ROS ReferenceManager
  auto rosReferenceManagerPtr = std::make_shared<RosReferenceManager>(robotName, leggedInterface_->getReferenceManagerPtr());
  rosReferenceManagerPtr->subscribe(nh);
  // mpc_->getSolverPtr()->addSynchronizedModule(gaitReceiverPtr);
  mpc_->getSolverPtr()->setReferenceManager(rosReferenceManagerPtr);
  observationPublisher_ = nh.advertise<ocs2_msgs::mpc_observation>(robotName + "_mpc_observation", 1);
}

void LeggedController::setupMrt() {
  mpcMrtInterface_ = std::make_shared<MPC_MRT_Interface>(*mpc_);
  mpcMrtInterface_->initRollout(&leggedInterface_->getRollout());
  mpcTimer_.reset();

  controllerRunning_ = true;
  mpcThread_ = std::thread([&]() {
    while (controllerRunning_) {
      try {
        executeAndSleep(
            [&]() {
              if (mpcRunning_) {
                mpcTimer_.startTimer();
                mpcMrtInterface_->advanceMpc();
                mpcTimer_.endTimer();
              }
            },
            leggedInterface_->mpcSettings().mpcDesiredFrequency_);
      } catch (const std::exception& e) {
        controllerRunning_ = false;
        ROS_ERROR_STREAM("[Ocs2 MPC thread] Error : " << e.what());
        stopRequest(ros::Time());
      }
    }
  });
  setThreadPriority(leggedInterface_->sqpSettings().threadPriority, mpcThread_);
}

void LeggedController::setupStateEstimate(const std::string& taskFile, bool verbose) {
  stateEstimate_ = std::make_shared<KalmanFilterEstimate>(leggedInterface_->getPinocchioInterface(),
                                                          leggedInterface_->getCentroidalModelInfo(), *eeKinematicsPtr_);
  stateEstimate_->loadSettings(taskFile, verbose);
  dynamic_cast<KalmanFilterEstimate&>(*stateEstimate_).loadSettings(taskFile, verbose);
  currentObservation_.time = 0;
}

void LeggedController::setupStateEstimateForRL(const std::string& taskFile, bool verbose) {
  stateEstimateRL_ = std::make_shared<ComputeForOdom>(leggedInterface_->getPinocchioInterface(),
                                                          leggedInterface_->getCentroidalModelInfo(), *eeKinematicsPtr_);
  stateEstimateRL_->loadSettings(taskFile, verbose);
  dynamic_cast<ComputeForOdom&>(*stateEstimateRL_).loadSettings(taskFile, verbose);
  currentObservation_RL_.time = 0;
}

void LeggedCheaterController::setupStateEstimate(const std::string& /*taskFile*/, bool /*verbose*/) {
  stateEstimate_ = std::make_shared<FromTopicStateEstimate>(leggedInterface_->getPinocchioInterface(),
                                                            leggedInterface_->getCentroidalModelInfo(), *eeKinematicsPtr_);
}

vector_array_t LeggedController::computeFeetPosAndVel(const vector_t& qPino, const vector_t& vPino) {
  const auto& model = leggedInterface_->getPinocchioInterface().getModel();
  auto& data = leggedInterface_->getPinocchioInterface().getData();
  pinocchio::forwardKinematics(model, data, qPino, vPino);
  pinocchio::updateFramePlacements(model, data);
  const auto eePos = eeKinematicsPtr_->getPosition(vector_t());
  const auto eeVel = eeKinematicsPtr_->getVelocity(vector_t(), vector_t());

  vector_t feet_position(12);
  vector_t feet_velocity(12);
  for (int i = 0; i < 4; i++)
  {
    feet_position.segment<3>(3*i) = eePos[i];
    feet_velocity.segment<3>(3*i) = eeVel[i];
  }
  return {feet_position, feet_velocity};
}


void LeggedController::limitJointPosAndTor(vector_t& jointPos,vector_t& jointTor,const scalar_t jointTorMax){
  // std::cout<<" limitJointPosAndTor: "<< jointTorMax<<std::endl;
  const auto& model = leggedInterface_->getPinocchioInterface().getModel();
  for(size_t j=0; j<12; ++j){
    const scalar_t lowerBound_ = model.lowerPositionLimit(6+j);
    const scalar_t upperBound_ = model.upperPositionLimit(6+j);
    if(jointPos[j]<lowerBound_||jointPos[j]>upperBound_||fabs(jointTor[j])>jointTorMax)
      // ROS_WARN_STREAM("[LEGGED CONTROL 22] LIMIT JOINT AND TOR ->] "<<j<<" desPos="<<jointPos[j]<<" desTor="<<jointTor[j]);
    
    jointPos[j] = jointPos[j]<lowerBound_? lowerBound_ : (jointPos[j]>upperBound_? upperBound_: jointPos[j]);
    jointTor[j] = jointTor[j]<-jointTorMax? -jointTorMax : (jointTor[j]>jointTorMax? jointTorMax : jointTor[j]);
  
  }
}

void LeggedController::setControllerPassive(const ros::Time& time){

  if(!enterPassiveModel_){
    passiveModelTimer_.startTimer();
    enterPassiveModel_=true;
    runningPositionMode = false;
    runningLiftedMode = false;
    runningTwoLegStandMode = false;
  }

  kp_stance = 0;
  kp_swing = 0;
  kd_stance = 4;
  kd_swing = 4;
  tau_des_filtered_.setZero();
  
  load_controller_flag = false;
  controller_msg_count = 0;
  leggedInterface_->getSwitchedModelReferenceManagerPtr()->setGaitType(0);  //stance

  std_msgs::Float32 centroidHeightData;
  centroidHeightData.data = 0.0;
  adjustCentroid_pub_.publish(centroidHeightData);

  mpcRunning_ = false; 
  ros::param::set("robotBodyRecoveryState","false");

  // stopping(time);
  // starting(time);

  ROS_ERROR_STREAM("[LEGGED CONTROL] ENTER Passive mode!!!");
  
}
  
void LeggedController::setControllerStop(){
  emergency_flag=true;
  kp_stance = 0;
  kp_swing = 0;
  kd_stance = 4;
  kd_swing = 4;
  tau_des_filtered_.setZero();
  load_controller_flag = false;
  



  ROS_ERROR_STREAM("[LEGGED CONTROL] Stop Controller!!!");
  
}


bool LeggedController::checkJointTorErr(const ros::Time& time,const vector_t& jointTorDes,double jointTorMax,double durationTime,double ErrThre)
{
  if(gravity_compensation_flag)
  {
    return false;
  }

  // vector_t jointTorCur(hybridJointHandles_.size());

  // for (size_t i = 0; i < hybridJointHandles_.size(); ++i) {
  //   bool result_ = fabs(hybridJointHandles_[i].getEffort())>=jointTorMax;
  //   if(result_&&!jointTorErrStart_)
  //   {
  //     jointTorErrStart_ = true;
  //     jointTorErrTimer_.startTimer();
  //   }
  //   if(jointTorErrStart_&&result_)
  //     ++jointTorErrCorNum_[i][0];
  //   else if(jointTorErrStart_&&!result_)
  //     ++jointTorErrCorNum_[i][1];

  // }

  // jointTorErrTimer_.endTimer();
  // if(jointTorErrStart_&&(jointTorErrTimer_.getLastIntervalInMilliseconds()/1000.0 > durationTime))
  // {
  //   for (size_t i = 0; i < hybridJointHandles_.size(); ++i)
  //   {
  //     double errPer_ = jointTorErrCorNum_[i][0]*1.0 /(jointTorErrCorNum_[i][0]+jointTorErrCorNum_[i][1]);
  //     std::fill(jointTorErrCorNum_[i].begin(),jointTorErrCorNum_[i].end(),0);
  //     jointTorErrStart_ = false;

  //     if(errPer_>=ErrThre)
  //     {
  //       std::cout<<"joint:"<<i<<" checkJointTorErr errPer_:"<<errPer_<<std::endl;
  //       ROS_ERROR_STREAM("[LEGGED CONTROL 27-30 ] -> WHEN UPDATE CONTROLLER JOINT TORQUE FAILED!!! is JOINT: "<<i);
  //       return true;
  //     }

  //   }
  // }
  return false;

}

// gaitType 0->walk,1->trot ,3->flying_trot ,mpcContactForce:LF, RF, LH, RH
bool LeggedController::checkMpcContactForceErr(const ros::Time& time, const vector_t& mpcContactForce,const int gaitType,double durationTime,double ErrThre)
{
  if(gravity_compensation_flag)
  {
    return false;
  }
  // if(conStartIntervalSec_<1)
  if(controlTimer_.getLastIntervalInMilliseconds()/1000.0 <1)
  {
    for(int i=0;i<4;i++){
      if(fabs(mpcContactForce(i*3))>6||fabs(mpcContactForce(i*3+1)>6)||fabs(mpcContactForce(i*3+2))>75){
        // ROS_WARN_STREAM("[LEGGED CONTROL 23] MPC ContactForce err in starting control -> leg: "<<i<<" x:"<<mpcContactForce(i*3)<<" y:"<<mpcContactForce(i*3+1)<<" z:"<<mpcContactForce(i*3+2));
        // errNum_++;
        return true;
      }
    }
  }
  
  // bool result_;
  // for(int i=0;i<4;i++)
  // {
  //   if(gaitType==3)   //flying_trot
  //     result_ = fabs(mpcContactForce(i*3))>130||fabs(mpcContactForce(i*3+1)>130)||fabs(mpcContactForce(i*3+2))>350;
  //   else if(gaitType==0||gaitType==1)   //trot
  //     result_ = fabs(mpcContactForce(i*3))>100||fabs(mpcContactForce(i*3+1)>100)||fabs(mpcContactForce(i*3+2))>300;
    
  //   if(result_&&!mpcContactForceErrStart_){
  //     // ROS_WARN_STREAM("[LEGGED CONTROL 23] MPC ContactForce err in update -> "<<" gaitType:"<<gaitType<<" leg:"<<i<<" x:"<<mpcContactForce(i*3)<<" y:"<<mpcContactForce(i*3+1)<<" z:"<<mpcContactForce(i*3+2));
  //     // errNum_++;
  //     mpcContactForceErrStart_ = true;
  //     mpcContactForceErrTimer_.startTimer();
  //   }
  //   if(mpcContactForceErrStart_&&result_)
  //   {
  //     ++mpcContactForceErrCorNum_[i][0];
  //     // std::cout<<" mpcContactForceErrCorNum_: leg->"<<i<<" num:"<<mpcContactForceErrCorNum_[i][0]<<std::endl;

  //   }
  //   else if(mpcContactForceErrStart_&&!result_)
  //     ++mpcContactForceErrCorNum_[i][1];
  // }

  // mpcContactForceErrTimer_.endTimer();
  // if(mpcContactForceErrStart_&&(mpcContactForceErrTimer_.getLastIntervalInMilliseconds()/1000.0 > durationTime))
  // {
  //   for(int j=0;j<4;j++)
  //   {

  //     double errPer_ = mpcContactForceErrCorNum_[j][0]*1.0 /(mpcContactForceErrCorNum_[j][0]+mpcContactForceErrCorNum_[j][1]);
  //     std::fill(mpcContactForceErrCorNum_[j].begin(),mpcContactForceErrCorNum_[j].end(),0);
  //     mpcContactForceErrStart_ = false;
  //     std::cout<<"leg:"<<j<<" MpcContactForceErr errPer_:"<<errPer_<<std::endl;

  //     if(errPer_>=ErrThre)
  //     {
  //       ROS_ERROR_STREAM("[LEGGED CONTROL -> WHEN UPDATE CONTROLLER] MPC CONTACT FORCE FAILED!!! is leg: "<<j);
  //       return true;
  //     }

  //   }
  // }
  

  return false;


}

bool LeggedController::checkWbcQpSolvedErr(const ros::Time& time,double durationTime,double ErrThre){
  // double intervelTime_;
  if(gravity_compensation_flag||runningPositionMode)
  {
    return false;
  }

  if(controller_switch_flag)
  {
    return false;
  }

  // check controller in start 
  if(controlTimer_.getLastIntervalInMilliseconds()/1000.0 <1&&!wbc_->getQpSolvedFlag())
  {
    ROS_ERROR_STREAM("[LEGGED CONTROL -> WHEN START CONTROLLER] WBC QP SOLVED FAILED!!!");
    return true;
  }


  // check controller in update
  if(!wbc_->getQpSolvedFlag()&&!wbcQqErrStart_)
  {
    wbcQqErrStart_ = true;
    // wbcQqErrStartTime = time.toSec();
    wbcQqErrTimer_.startTimer();
    std::cout<<" ###wbcQqErrStart_"<<std::endl;

  }
  if(wbcQqErrStart_&&!wbc_->getQpSolvedFlag()){
    ++wbcQqErrCorNum_[0];
    // std::cout<<" wbcQqErrCorNum_: "<<wbcQqErrCorNum_[0]<<std::endl;
  }
  else if(wbcQqErrStart_&&wbc_->getQpSolvedFlag()){
    ++wbcQqErrCorNum_[1];
  }
  // double intervelTime_ = time.toSec() - wbcQqErrStartTime;
  wbcQqErrTimer_.endTimer();
  // if(wbcQqErrStart_&&(intervelTime_ > durationTime))
  if(wbcQqErrStart_&&(wbcQqErrTimer_.getLastIntervalInMilliseconds()/1000.0 > durationTime))
  {
    double errPer_ = wbcQqErrCorNum_[0]*1.0 /(wbcQqErrCorNum_[0]+wbcQqErrCorNum_[1]);
    std::fill(wbcQqErrCorNum_.begin(),wbcQqErrCorNum_.end(),0);
    wbcQqErrStart_ = false;
    // std::cout<<" \n[checkWbcQpSolved] -> errNum:"<<wbcQqErrCorNum_[0]<<" corNum:"<<wbcQqErrCorNum_[1]<<" errPer:"<<errPer_<<std::endl;
    std::cout<<" checkWbcQpSolvedErr errPer_:"<<errPer_<<std::endl;

    if(errPer_>=ErrThre)
    {
      ROS_ERROR_STREAM("[LEGGED CONTROL -> WHEN UPDATE CONTROLLER] WBC QP SOLVED FAILED!!!");
      return true;
    }
  }
  
  return false;
}

bool LeggedController::checkRobotLifted(const ros::Time& time, const vector_t& footPos, const contact_flag_t& cmdContactFlag) {
  if(controller_switch_flag){
    return false;
  }
  if(gravity_compensation_flag || !lifted_detection_enable || runningPositionMode) //  || runningLiftedMode)
    return false;

  if(!load_controller_flag || controlTimer_.getLastIntervalInMilliseconds() * 0.001 < mode_transition_time_lifted) // avoid change from position control to force control
    return false;

  // todo add slope motion lifted detection
  // check if we need to add torque conditions

  static int lose_contact_count[4] = {0, 0, 0, 0};

  auto contacForce = stateEstimate_->getEstContactForce();
  auto modelInfo = leggedInterface_->getCentroidalModelInfo();
  bool robotLifted = false;

  Eigen::Vector3d foot_pos_body_aligned_in_world[4];

  Eigen::Vector3d foot_pos_sum, foot_pos_average, foot_pos_front_average, foot_pos_rear_average, 
                  foot_pos_left_average, foot_pos_right_average;
  foot_pos_sum.setZero();
  foot_pos_average.setZero();
  foot_pos_front_average.setZero();
  foot_pos_rear_average.setZero();
  foot_pos_left_average.setZero();
  foot_pos_right_average.setZero();

  Eigen::Vector3d feet_bias[4];
  feet_bias[0] << feet_bias_x, feet_bias_y, 0;    // lf
  feet_bias[1] << -feet_bias_x, feet_bias_y, 0;   // lh
  feet_bias[2] << feet_bias_x, -feet_bias_y, 0;   // rf
  feet_bias[3] << -feet_bias_x, -feet_bias_y, 0;  // rh

  auto gaitTy_ = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getGaitType();
  auto start_stop_time = leggedInterface_->getSwitchedModelReferenceManagerPtr()->getSwingTrajectoryPlanner()->threadSaftyGetStartStopTime(currentObservation_.time);
  Eigen::Matrix3d RotMatBody = getRotationMatrixFromZyxEulerAngles<double>(measuredRbdState_.segment<3>(0));

  for(int i = 0; i < 4; i++) {
    feet_bias[i] = RotMatBody * feet_bias[i];
  }

  for (int i = 0; i < 4; i++) {
    foot_pos_body_aligned_in_world[i] = footPos.segment<3>(3 * i) - measuredRbdState_.segment<3>(3) - feet_bias[i];
    foot_pos_sum += foot_pos_body_aligned_in_world[i];
  }

  foot_pos_average = 0.25 * foot_pos_sum;
  foot_pos_front_average = 0.5 * (foot_pos_body_aligned_in_world[0] + foot_pos_body_aligned_in_world[2]);
  foot_pos_rear_average = 0.5 * (foot_pos_body_aligned_in_world[1] + foot_pos_body_aligned_in_world[3]);
  foot_pos_left_average = 0.5 * (foot_pos_body_aligned_in_world[0] + foot_pos_body_aligned_in_world[1]);
  foot_pos_right_average = 0.5 * (foot_pos_body_aligned_in_world[2] + foot_pos_body_aligned_in_world[3]);

  std_msgs::Float64MultiArray lifted_test_msg;
  lifted_test_msg.data.resize(16);

  for(int i = 0; i < 4; i ++) {
    if (cmdContactFlag[i]) { // rh lh rf lf
      // robotMass * 9.81 for avago is 177.2N
      // std::cout << modelInfo.robotMass * 9.81 * 0.5 << std::endl;
      double duration = start_stop_time[i].back() - start_stop_time[i].front();
      double lose_contact_treshold = duration * k_lose_contact_factor_ / 0.002;
      // std::cout << "lose_contact_treshold:" << lose_contact_treshold << std::endl;

      if (gaitTy_ == 13 && (i == 0 || i == 1)) { // stretch avoid check front legs
        continue;
      }

      if (currentObservation_.time <= start_stop_time[i].back() - phase_factor_lifted * duration && currentObservation_.time >= start_stop_time[i].front() + phase_factor_lifted * duration) {
        if(contacForce(12 + i) < modelInfo.robotMass * 9.81 * gravity_factor_lifted) {
          lose_contact_count[i]++;
          
          if(lose_contact_count[i] > std::ceil(lose_contact_treshold)){
            if(debug_print_lifted)
              std::cout<<"!!!!  God damn, who the mother fucker picked me up, dog hooves lost contact   !!!!"<<std::endl;
            robotLifted = true;
          }
        } else {
          lose_contact_count[i] = 0;
        }
      }
    }
    if(foot_pos_body_aligned_in_world[i].norm() > max_leg_length_lifted && (std::abs(foot_pos_front_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_rear_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_left_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_right_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_average[2]) > max_foot_pos_z_lifted)) {
      if(debug_print_lifted)
        std::cout<<"!!!!  God damn, who the mother fucker picked me up, dog bones almost broken   !!!!"<<std::endl;
      robotLifted = true;
    }
    lifted_test_msg.data[i * 3 + 0] = foot_pos_body_aligned_in_world[i][0];
    lifted_test_msg.data[i * 3 + 1] = foot_pos_body_aligned_in_world[i][1];
    lifted_test_msg.data[i * 3 + 2] = foot_pos_body_aligned_in_world[i][2];
  }

  lifted_test_msg.data[12] = foot_pos_body_aligned_in_world[0].norm();
  lifted_test_msg.data[13] = foot_pos_body_aligned_in_world[1].norm();
  lifted_test_msg.data[14] = foot_pos_body_aligned_in_world[2].norm();
  lifted_test_msg.data[15] = foot_pos_body_aligned_in_world[3].norm();

  testLiftedPublisher_.publish(lifted_test_msg);
  return robotLifted;
}

bool LeggedController::checkLiftedinRL(const ros::Time& time, const vector_t& footPos) {

  static int lose_contact_count[4] = {0, 0, 0, 0};

  auto contacForce = stateEstimate_->getEstContactForce();
  auto contactFlag = stateEstimate_->getRLContactState();
  auto modelInfo = leggedInterface_->getCentroidalModelInfo();
  bool robotLifted = false;

  Eigen::Vector3d foot_pos_body_aligned_in_world[4];

  Eigen::Vector3d foot_pos_sum, foot_pos_average, foot_pos_front_average, foot_pos_rear_average, 
                  foot_pos_left_average, foot_pos_right_average;
  foot_pos_sum.setZero();
  foot_pos_average.setZero();
  foot_pos_front_average.setZero();
  foot_pos_rear_average.setZero();
  foot_pos_left_average.setZero();
  foot_pos_right_average.setZero();

  Eigen::Vector3d feet_bias[4];
  feet_bias[0] << feet_bias_x, feet_bias_y, 0;    // lf
  feet_bias[1] << -feet_bias_x, feet_bias_y, 0;   // lh
  feet_bias[2] << feet_bias_x, -feet_bias_y, 0;   // rf
  feet_bias[3] << -feet_bias_x, -feet_bias_y, 0;  // rh

  Eigen::Matrix3d RotMatBody = getRotationMatrixFromZyxEulerAngles<double>(measuredRbdStateRL_.segment<3>(0));

  for(int i = 0; i < 4; i++) {
    feet_bias[i] = RotMatBody * feet_bias[i];
  }

  for (int i = 0; i < 4; i++) {
    foot_pos_body_aligned_in_world[i] = footPos.segment<3>(3 * i) - measuredRbdStateRL_.segment<3>(3) - feet_bias[i];
    foot_pos_sum += foot_pos_body_aligned_in_world[i];
  }

  foot_pos_average = 0.25 * foot_pos_sum;
  foot_pos_front_average = 0.5 * (foot_pos_body_aligned_in_world[0] + foot_pos_body_aligned_in_world[2]);
  foot_pos_rear_average = 0.5 * (foot_pos_body_aligned_in_world[1] + foot_pos_body_aligned_in_world[3]);
  foot_pos_left_average = 0.5 * (foot_pos_body_aligned_in_world[0] + foot_pos_body_aligned_in_world[1]);
  foot_pos_right_average = 0.5 * (foot_pos_body_aligned_in_world[2] + foot_pos_body_aligned_in_world[3]);

  std_msgs::Float64MultiArray lifted_test_msg;
  lifted_test_msg.data.resize(16);

  for(int i = 0; i < 4; i ++) {
    if (contactFlag[i]) { // rh lh rf lf
      // robotMass * 9.81 for avago is 177.2N
      // std::cout << modelInfo.robotMass * 9.81 * 0.5 << std::endl;


        if(contacForce(12 + i) < modelInfo.robotMass * 9.81 * gravity_factor_lifted) {
          lose_contact_count[i]++;
          
          if(lose_contact_count[i] > 100){
            if(debug_print_lifted)
              std::cout<<"!!!!  God damn, who the mother fucker picked me up, dog hooves lost contact   !!!!"<<std::endl;
              std::cout << "contact force: " << contacForce(12 + i) << std::endl;
            robotLifted = true;
          }
        } else {
          lose_contact_count[i] = 0;
        }
    }
    if(foot_pos_body_aligned_in_world[i].norm() > max_leg_length_lifted && (std::abs(foot_pos_front_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_rear_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_left_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_right_average[2]) > max_foot_pos_z_lifted || std::abs(foot_pos_average[2]) > max_foot_pos_z_lifted)) {
      if(debug_print_lifted)
        std::cout<<"!!!!  God damn, who the mother fucker picked me up, dog bones almost broken   !!!!"<<std::endl;
        foot_pos_body_aligned_in_world[i].norm() > max_leg_length_lifted ? std::cout << "foot_pos_body_aligned_in_world[i].norm()" << foot_pos_body_aligned_in_world[i].norm() : std::cout << "";
      std::abs(foot_pos_front_average[2]) > max_foot_pos_z_lifted ? std::cout << "foot_pos_front_average[2]" << foot_pos_front_average[2] : std::cout << "";
      std::abs(foot_pos_rear_average[2]) > max_foot_pos_z_lifted ? std::cout << "foot_pos_rear_average[2]" << foot_pos_rear_average[2] : std::cout << "";
      std::abs(foot_pos_left_average[2]) > max_foot_pos_z_lifted ? std::cout << "foot_pos_left_average[2]" << foot_pos_left_average[2] : std::cout << "";
      std::abs(foot_pos_right_average[2]) > max_foot_pos_z_lifted ? std::cout << "foot_pos_right_average[2]" << foot_pos_right_average[2] : std::cout << "";
      robotLifted = true;
    }
    lifted_test_msg.data[i * 3 + 0] = foot_pos_body_aligned_in_world[i][0];
    lifted_test_msg.data[i * 3 + 1] = foot_pos_body_aligned_in_world[i][1];
    lifted_test_msg.data[i * 3 + 2] = foot_pos_body_aligned_in_world[i][2];
  }

  lifted_test_msg.data[12] = foot_pos_body_aligned_in_world[0].norm();
  lifted_test_msg.data[13] = foot_pos_body_aligned_in_world[1].norm();
  lifted_test_msg.data[14] = foot_pos_body_aligned_in_world[2].norm();
  lifted_test_msg.data[15] = foot_pos_body_aligned_in_world[3].norm();

  testLiftedPublisher_.publish(lifted_test_msg);
  return robotLifted;
}

void LeggedController::onEnterLiftedMode(const ros::Time& time) {
  if (!runningLiftedMode) {
    // record x y z r p y jp(12) ,  lf lh rf rh
    enterLiftedJointPos_ = measuredRbdState_.segment<12>(6);
    // hip
    cmdLiftedJointPos_[0] = hip_pos_lifted;
    cmdLiftedJointPos_[3] = hip_pos_lifted;
    cmdLiftedJointPos_[6] = -hip_pos_lifted;
    cmdLiftedJointPos_[9] = -hip_pos_lifted;

    // thigh
    cmdLiftedJointPos_[1] = thigh_pos_lifted;
    cmdLiftedJointPos_[4] = thigh_pos_lifted;
    cmdLiftedJointPos_[7] = thigh_pos_lifted;
    cmdLiftedJointPos_[10] = thigh_pos_lifted;

    // calf
    cmdLiftedJointPos_[2] = calf_pos_lifted;
    cmdLiftedJointPos_[5] = calf_pos_lifted;
    cmdLiftedJointPos_[8] = calf_pos_lifted;
    cmdLiftedJointPos_[11] = calf_pos_lifted;

    enterLiftedTime = time;
    mpcRunning_ = false;
    runningLiftedMode = true;
    runningPositionMode = true;
  } else {
    return;
  }
}

bool LeggedController::checkJointPosNear(const vector_t &pos1, const vector_t &pos2) {
  for(int i = 0; i < 12; i++) {
    
    if(std::abs(pos1[i] - pos2[i]) >= 0.2) { // according to machine
      std::cout<<"dif i:"<<i<<" "<<std::abs(pos1[i] - pos2[i])<<std::endl;
      return false;
    }
  }
  return true;
}

void LeggedController::onExitLiftedMode(const ros::Time& time) {
  static int recover_contact_count[4] = {0, 0, 0, 0};

  if(runningLiftedMode) {
    auto contacForce = stateEstimate_->getEstContactForce();
    auto modelInfo = leggedInterface_->getCentroidalModelInfo();
    
    if(std::abs(measuredRbdState_[2]) < 0.1 &&
       std::abs(measuredRbdState_[1]) < 0.1 &&
       checkJointPosNear(measuredRbdState_.segment<12>(6), cmdLiftedJointPos_)) {
      bool recover_stand = true;
      for(int i = 0; i < 4; i++) {
        if (contacForce(12 + i) <= modelInfo.robotMass * 9.81 * gravity_factor_lifted_recover){
          recover_contact_count[i] = 0;
        } else {
          recover_contact_count[i]++;
        }

        if(recover_contact_count[i] < 10) {
          recover_stand = false;
        }
      }
      
      if (recover_stand) {
        currentObservation_.input.setZero(leggedInterface_->getCentroidalModelInfo().inputDim);
        TargetTrajectories target_trajectories({currentObservation_.time}, {currentObservation_.state}, {currentObservation_.input});
        currentObservation_.mode = ModeNumber::STANCE;

        // mpcMrtInterface_->setCurrentObservation(currentObservation_);
        mpcMrtInterface_->getReferenceManager().setTargetTrajectories(target_trajectories);
        mpcRunning_ = true;
        runningLiftedMode = false;
        runningPositionMode = false;
      }
    }
  } else {
    return;
  }
}

void LeggedController::onEnterPosMode(int mode, double transition_time) {
  enterPosModeJointPos_ = jointPos_;
  switch(mode){
    case 1:
      kp_pos_mode_ = kp_hug_me_baby_;
      kd_pos_mode_ = kd_hug_me_baby_;
      cmdPosModeJointPos_ = hugMeBabyModeJointPos_;
      break;
    default:
      kp_pos_mode_ = kp_lifted;
      kd_pos_mode_ = kd_lifted;
      cmdPosModeJointPos_ = jointPos_;
      break;
  }

  pos_transition_time_ = transition_time;

  enterPosModeTime = ros::Time::now();
  mpcRunning_ = false;
}

void LeggedController::onExitPosMode() {
  if(std::abs(measuredRbdState_[2]) < 0.1 &&
      std::abs(measuredRbdState_[1]) < 0.1 &&
      checkJointPosNear(measuredRbdState_.segment<12>(6), cmdPosModeJointPos_)) {

    auto des_states = currentObservation_.state;
    des_states[8] = comHeightRef * 0.8; // recover height
    currentObservation_.input.setZero(leggedInterface_->getCentroidalModelInfo().inputDim);
    TargetTrajectories target_trajectories({currentObservation_.time}, {des_states}, {currentObservation_.input});

    currentObservation_.mode = ModeNumber::STANCE;
    // mpcMrtInterface_->setCurrentObservation(currentObservation_);
    // leggedInterface_->getSwitchedModelReferenceManagerPtr()->setGaitType(0);                  // recover standup
    mpcMrtInterface_->getReferenceManager().setTargetTrajectories(target_trajectories);
    mpcRunning_ = true;
  }
}

void LeggedController::onEnterTwoLegStandMode() {
  if(runningTwoLegStandMode) return;
  if(std::abs(measuredRbdState_[2]) > 0.55 || std::abs(measuredRbdState_[1]) > 0.45) {
    printf("It's not safe to do happy new year\n");
    return;
  }

  twoLegStandStage_ = 0;            // enter next stage push
  enterTwoLegStandModeJointPos_ = jointPos_;
  enterTwoLegStandModeTime = ros::Time::now();
  runningTwoLegStandMode = true;
  runningPositionMode = true;
  mpcRunning_ = false;
}

void LeggedController::onExitTwoLegStandMode() {
  // std::cout<<" t1"<<std::endl;
  if(!runningTwoLegStandMode) return;

  auto des_states = currentObservation_.state;
  des_states[8] = comHeightRef * 0.85; // recover height
  currentObservation_.input.setZero(leggedInterface_->getCentroidalModelInfo().inputDim);
  TargetTrajectories target_trajectories({currentObservation_.time}, {des_states}, {currentObservation_.input});

  currentObservation_.mode = ModeNumber::STANCE;
  // mpcMrtInterface_->setCurrentObservation(currentObservation_);
  // leggedInterface_->getSwitchedModelReferenceManagerPtr()->setGaitType(0);                  // recover standup
  mpcMrtInterface_->getReferenceManager().setTargetTrajectories(target_trajectories);

  runningTwoLegStandMode = false;
  // std::cout<<" runningPositionMode:"<<runningPositionMode<<std::endl;
  runningPositionMode = false;
  mpcRunning_ = true;
  // std::cout<<" t2"<<std::endl;

}

void LeggedController::runTwoLegStandAdjustStage(const ros::Time& time) {
  if(time.toSec() - enterTwoLegStandModeTime.toSec() <= adjust_duration_) {
    for (int i = 0; i < 12; i++) {
      cmdTwoLegStandModeKp_[i] = kp_adjust_stage_;
      cmdTwoLegStandModeKd_[i] = kd_adjust_stage_;
    }

    for(int i = 0; i < 3; i++) {
      vector3_t des_qf = vector3_t::Zero();
      vector3_t des_qr = vector3_t::Zero();

      des_qf << 0, des_thigh_pos_adjust_stage_f_, des_calf_pos_adjust_stage_f_;
      des_qr << 0, des_thigh_pos_adjust_stage_r_, des_calf_pos_adjust_stage_r_;

      cmdTwoLegStandModeJointPos_[i] = enterTwoLegStandModeJointPos_[i] + (des_qf[i] - enterTwoLegStandModeJointPos_[i]) * (time.toSec() - enterTwoLegStandModeTime.toSec()) / adjust_duration_;
      cmdTwoLegStandModeJointPos_[i + 6] = enterTwoLegStandModeJointPos_[i + 6] + (des_qf[i] - enterTwoLegStandModeJointPos_[i + 6]) * (time.toSec() - enterTwoLegStandModeTime.toSec()) / adjust_duration_;
      cmdTwoLegStandModeJointPos_[i + 3] = enterTwoLegStandModeJointPos_[i + 3] + (des_qr[i] - enterTwoLegStandModeJointPos_[i + 3]) * (time.toSec() - enterTwoLegStandModeTime.toSec()) / adjust_duration_;
      cmdTwoLegStandModeJointPos_[i + 9] = enterTwoLegStandModeJointPos_[i + 9] + (des_qr[i] - enterTwoLegStandModeJointPos_[i + 9]) * (time.toSec() - enterTwoLegStandModeTime.toSec()) / adjust_duration_;
    }
    cmdTwoLegStandModeTau_.setZero();
  } else {
    front_legs_force_to_pos_ = false;
    push_finished_[0] = false;
    push_finished_[1] = false;
    twoLegStandStage_ = 1;            // enter next stage push
    enterTwoLegStandModeTime = time;  // re record time
    twoLegStandModeLeaveGroundTime = 0.0;
  }
}

void LeggedController::runTwoLegStandPushStage(const ros::Time& time) {
  static double push_pitch_vel = -0.3;
  static Eigen::Vector3d joint_pos_tmp_front[2];
  static Eigen::Vector3d joint_pos_tmp_rear[2];
  static Eigen::Vector3d joint_pos_tmp_rear2[2];

  Eigen::Vector3d leg_joint_pos[2];
  Eigen::Vector3d foot_pos[2];
  Eigen::Matrix3d foot_jacobi[2];
  for (int i = 0; i < 2; i++) {
    leg_joint_pos[i] = jointPos_.segment(i * 6, 3);
    foot_pos[i] = singleLegKinematic_.forward(leg_joint_pos[i]);
    foot_jacobi[i] = singleLegKinematic_.jacobi(leg_joint_pos[i]);
  }

  double cur_time = (time.toSec() - enterTwoLegStandModeTime.toSec());
  double exert_phase = cur_time > exert_force_duration_ ? 1 : cur_time / exert_force_duration_; //0.2
  double push_reach_phase = cur_time > 2 * push_duration_ ? 1 : (cur_time - push_duration_) / push_duration_; //0.7  *2

  Eigen::Vector3d des_push_force = Eigen::Vector3d::Zero();
  des_push_force[2] = exert_phase * (push_force_target_ - push_force_init_) + push_force_init_;//push_force_target:56,push_force_init:34

  for (int i = 0; i < 4; i++) {
    if (i == 0 || i == 2) {  // front legs
      int j = i / 2;
      if(std::abs(foot_pos[j][2]) <= 0.82 * (thigh_length_ + calf_length_) && !push_finished_[0] && !push_finished_[1]) {
        cmdTwoLegStandModeTau_.segment(i * 3, 3) = -foot_jacobi[j].transpose() * des_push_force;
        cmdTwoLegStandModeKp_[i * 3 + 2] = 0;
        cmdTwoLegStandModeKp_[i * 3 + 1] = 0;
        cmdTwoLegStandModeKp_[i * 3] = kp_hip_exert_stage_;

        cmdTwoLegStandModeKd_[i * 3 + 2] = 0;
        cmdTwoLegStandModeKd_[i * 3 + 1] = 0;
        cmdTwoLegStandModeKd_[i * 3] = kd_hip_exert_stage_;
        cmdTwoLegStandModeJointPos_.segment(i * 3, 3) = Eigen::Vector3d::Zero();
      } else {
        if(!front_legs_force_to_pos_) {
          for (int i = 0; i < 2; i++) {
            joint_pos_tmp_front[i] = jointPos_.segment(i * 6, 3); // record front legs joint pos
            joint_pos_tmp_rear[i] = jointPos_.segment(i * 6 + 3, 3);
          }
          twoLegStandModeLeaveGroundTime = cur_time;
          front_legs_force_to_pos_ = true;
          push_pitch_vel = vPino_[4];
        }

        if(cur_time < push_duration_) { // reach early
          cmdTwoLegStandModeJointPos_.segment(i * 3, 3) = joint_pos_tmp_front[j];
          push_finished_[j] = true;
        } else { // try to reach target
          vector3_t des_q = vector3_t::Zero();
          double des_hip_here = i >= 2? des_front_hip_pos_push_stage_: -des_front_hip_pos_push_stage_;
          des_q << des_hip_here, des_front_thigh_pos_push_stage_, des_front_calf_pos_push_stage_;

          cmdTwoLegStandModeJointPos_.segment(i * 3, 3) = (des_q - joint_pos_tmp_front[j]) * push_reach_phase + joint_pos_tmp_front[j];
          push_finished_[j] = true;
        }

        cmdTwoLegStandModeKp_[i * 3 + 2] = kp_calf_push_stage_;
        cmdTwoLegStandModeKp_[i * 3 + 1] = kp_thigh_push_stage_;
        cmdTwoLegStandModeKp_[i * 3] = kp_hip_push_stage_;

        cmdTwoLegStandModeKd_[i * 3 + 2] = kd_calf_push_stage_;
        cmdTwoLegStandModeKd_[i * 3 + 1] = kd_thigh_push_stage_;
        cmdTwoLegStandModeKd_[i * 3] = kd_hip_push_stage_;
        cmdTwoLegStandModeTau_.segment(i * 3, 3) = Eigen::Vector3d::Zero();
      }
    }
    if (i == 1 || i == 3) { // rear legs
      int j = i / 2;
      if (!push_finished_[0] || !push_finished_[1]) {
        double body_pitch = measuredRbdState_[1];
        double hip_pos_here      = jointPos_[i * 3 + 1];
        double des_knee_pos_here     = -(M_PI / 2.0 + hip_pos_here + body_pitch) + calf_link_pitch_;
        double leave_ground_time = twoLegStandModeLeaveGroundTime;

        vector3_t des_q = vector3_t::Zero();
        vector3_t tau_goal = vector3_t::Zero();
        tau_goal << 0, torque_rear_thigh_push_stage_, torque_rear_calf_push_stage_;
        des_q << 0, des_thigh_pos_adjust_stage_r_ - body_pitch, des_knee_pos_here;  // todo check here

        cmdTwoLegStandModeJointPos_.segment(i * 3, 3) = des_q;
        cmdTwoLegStandModeTau_.segment(i * 3, 3) = tau_goal;

        cmdTwoLegStandModeKp_[i * 3 + 2] = kp_calf_push_stage_rear_;
        cmdTwoLegStandModeKp_[i * 3 + 1] = kp_thigh_push_stage_rear_;
        cmdTwoLegStandModeKp_[i * 3] = kp_hip_push_stage_rear_;

        cmdTwoLegStandModeKd_[i * 3 + 2] = kd_calf_push_stage_rear_;
        cmdTwoLegStandModeKd_[i * 3 + 1] = kd_thigh_push_stage_rear_;
        cmdTwoLegStandModeKd_[i * 3] = kd_hip_push_stage_rear_;
      } else if (cur_time > twoLegStandModeLeaveGroundTime && cur_time <= twoLegStandModeLeaveGroundTime + rear_legs_adjust_duration_) {
        double leave_ground_time = twoLegStandModeLeaveGroundTime;
        double jump_pitch_duration = std::abs(des_back_thigh_pos_push_stage_ - joint_pos_tmp_rear[j][1]) / std::abs(push_pitch_vel);
        
        vector3_t des_q = joint_pos_tmp_rear[j];
        des_q[1] = joint_pos_tmp_rear[j][1] + (des_back_thigh_pos_push_stage_ - joint_pos_tmp_rear[j][1]) * ((cur_time - leave_ground_time) > jump_pitch_duration ? 1 : (cur_time - leave_ground_time) / jump_pitch_duration);
        des_q[2] = joint_pos_tmp_rear[j][2] + (des_back_calf_pos_push_stage_ - joint_pos_tmp_rear[j][2]) * ((cur_time - leave_ground_time) > jump_pitch_duration ? 1 : (cur_time - leave_ground_time) / jump_pitch_duration);
        cmdTwoLegStandModeJointPos_.segment(i * 3, 3) = des_q;

        cmdTwoLegStandModeKp_[i * 3 + 2] = kp_calf_push_adjust_stage_rear_;
        cmdTwoLegStandModeKp_[i * 3 + 1] = kp_thigh_push_adjust_stage_rear_;
        cmdTwoLegStandModeKp_[i * 3] = kp_hip_push_adjust_stage_rear_;

        vector3_t kd_init_here = vector3_t::Zero();
        vector3_t kd_target_here = vector3_t::Zero();
        vector3_t tau_init = vector3_t::Zero();
        vector3_t tau_goal = vector3_t::Zero();

        kd_init_here << kd_hip_push_adjust_stage_rear1_, kd_thigh_push_adjust_stage_rear1_, kd_calf_push_adjust_stage_rear1_;
        kd_target_here << kd_hip_push_adjust_stage_rear2_, kd_thigh_push_adjust_stage_rear2_, kd_calf_push_adjust_stage_rear2_;
        auto kdJoint_des = kd_init_here + (kd_target_here - kd_init_here) * ((cur_time - leave_ground_time) > jump_pitch_duration ? 1 : (cur_time - leave_ground_time) / jump_pitch_duration);
        cmdTwoLegStandModeKd_[i * 3 + 2] = kdJoint_des[2];
        cmdTwoLegStandModeKd_[i * 3 + 1] = kdJoint_des[1];
        cmdTwoLegStandModeKd_[i * 3] = kdJoint_des[0];

        tau_init << 0, torque_rear_thigh_push_stage_, torque_rear_calf_push_stage_;
        tau_goal << 0, torque_rear_thigh_keep_stage_, 0;   // for slow down
        auto tau_des = tau_init + (tau_goal - tau_init) * ((cur_time - leave_ground_time) > jump_pitch_duration ? 1 : (cur_time - leave_ground_time) / jump_pitch_duration);
        cmdTwoLegStandModeTau_.segment(i * 3, 3) = tau_des;

        joint_pos_tmp_rear2[j] = jointPos_.segment(i * 6 + 3, 3);
        twoLegStandModeReachTargetTime = cur_time;
      } else { // keep balance when push finished totally
        if (rear_legs_keep_duration_ <= twoLegStandModeLeaveGroundTime + rear_legs_adjust_duration_) {
          rear_legs_keep_duration_ = 0.6 + twoLegStandModeLeaveGroundTime + rear_legs_adjust_duration_;
        }

        double keep_still_duration = rear_legs_keep_duration_ - twoLegStandModeLeaveGroundTime - rear_legs_adjust_duration_;
        auto leg_angle_des  = joint_pos_tmp_rear2[j];

        cmdTwoLegStandModeKp_[i * 3 + 2] = kp_calf_push_adjust_stage_rear_;
        cmdTwoLegStandModeKp_[i * 3 + 1] = kp_thigh_push_adjust_stage_rear_;
        cmdTwoLegStandModeKp_[i * 3] = kp_hip_push_adjust_stage_rear_;

        vector3_t kd_init_here = vector3_t::Zero();
        vector3_t kd_target_here = vector3_t::Zero();
        vector3_t tau_init = vector3_t::Zero();
        vector3_t tau_goal = vector3_t::Zero();
        kd_target_here << kd_hip_push_adjust_stage_rear2_, kd_thigh_push_adjust_stage_rear2_, kd_calf_push_adjust_stage_rear2_;
        kd_init_here << kd_hip_push_adjust_stage_rear2_, kd_thigh_push_adjust_stage_rear2_, kd_calf_push_adjust_stage_rear2_;

        auto kdJoint_des = kd_init_here + (kd_target_here - kd_init_here) * ((cur_time - twoLegStandModeReachTargetTime) > keep_still_duration ? 1 : (cur_time - twoLegStandModeReachTargetTime) / keep_still_duration);
        cmdTwoLegStandModeKd_[i * 3 + 2] = kdJoint_des[2];
        cmdTwoLegStandModeKd_[i * 3 + 1] = kdJoint_des[1];
        cmdTwoLegStandModeKd_[i * 3] = kdJoint_des[0];

        tau_init << 0, torque_rear_thigh_keep_stage_, 0;
        tau_goal << 0, torque_rear_thigh_keep_stage_, 0;
        auto tau_des = tau_init + (tau_goal - tau_init) * ((cur_time - twoLegStandModeReachTargetTime) > keep_still_duration ? 1 : (cur_time - twoLegStandModeReachTargetTime) / keep_still_duration);
        cmdTwoLegStandModeTau_.segment(i * 3, 3) = tau_des;
      }
    }
  }
  
  if(std::abs(measuredRbdState_[1] + 1.3) < 0.18 && cur_time >= rear_legs_keep_duration_) { // 推到位了
    twoLegStandStage_ = 2;            // enter next stage push
    enterTwoLegStandModeTime = time;  // re record time
  }
}

void LeggedController::runHappyNewYear(const ros::Time& time) {
  Eigen::Vector3d leg_joint_pos[2];
  Eigen::Vector3d foot_pos[2];
  Eigen::Vector3d foot_pos_des[2];

  for (int i = 0; i < 2; i++) {
    leg_joint_pos[i] = jointPos_.segment(i * 6, 3);
    foot_pos[i] = singleLegKinematic_.forward(leg_joint_pos[i]);
  }

  double cur_time = time.toSec() - enterTwoLegStandModeTime.toSec();
  static Eigen::Vector3d foot_pos_init[2];
  static Eigen::Vector3d foot_pos_goal[2];

  if (cur_time <= 0.004) { // 实物是0.002的控制周期，这里必须两个控制周期，保证初值被读到
    for (int i = 0; i < 2; i++) {
      foot_pos_init[i] = foot_pos[i];
      foot_pos_goal[i] = foot_pos[i];
    }
  }

  int n = 0;

  for (int i = 0; i < 2; i++) {
    double side_sign = i == 0 ? 1 : -1;  // 左腿为正

    if (cur_time < happy_new_year_swing_duration_) {
      foot_pos_goal[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
    } else if (cur_time < 2 * happy_new_year_swing_duration_) {
      n = 1;
      foot_pos_init[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
      foot_pos_goal[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
    } else if (cur_time < 3 * happy_new_year_swing_duration_) {
      n = 2;
      foot_pos_init[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
      foot_pos_goal[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
    } else if (cur_time < 4 * happy_new_year_swing_duration_) {
      n = 3;
      foot_pos_init[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
      foot_pos_goal[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
    } else if (cur_time < 5 * happy_new_year_swing_duration_ ) {
      n = 4;
      foot_pos_init[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
      foot_pos_goal[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
    } else if (cur_time < 6 * happy_new_year_swing_duration_) {
      n = 5;
      foot_pos_init[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
      foot_pos_goal[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
    } else if (cur_time < 7 * happy_new_year_swing_duration_) {
      n = 6;
      foot_pos_init[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
      foot_pos_goal[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
    } else if (cur_time < 8 * happy_new_year_swing_duration_) {
      n = 7;
      foot_pos_init[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
      foot_pos_goal[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
    } else if (cur_time < 9 * happy_new_year_swing_duration_) {
      n = 8;
      foot_pos_init[i] << happy_new_year_pos2_x_, side_sign * happy_new_year_pos2_y_, happy_new_year_pos2_z_;
      foot_pos_goal[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
    } else if (cur_time < 10 * happy_new_year_swing_duration_) {
      n = 9;
      foot_pos_init[i] << happy_new_year_pos1_x_, side_sign * happy_new_year_pos1_y_, happy_new_year_pos1_z_;
      foot_pos_goal[i] << 0, 0, happy_new_year_pos1_z_;
    }
    else {
      twoLegStandStage_ = -1;            // exit two leg stand
      enterTwoLegStandModeJointPos_ = jointPos_;
      enterTwoLegStandModeTime = ros::Time::now();
    }
    foot_pos_des[i] = foot_pos_init[i] + (foot_pos_goal[i] - foot_pos_init[i]) * ((cur_time - n * happy_new_year_swing_duration_) > happy_new_year_swing_duration_ ? 1 : (cur_time - n * happy_new_year_swing_duration_ ) / happy_new_year_swing_duration_);
  }

  auto des_joint_pos_lf = singleLegKinematic_.inverse(foot_pos_des[0]);
  auto des_joint_pos_rf = singleLegKinematic_.inverse(foot_pos_des[1]);

  cmdTwoLegStandModeJointPos_.segment(0 * 3, 3) = des_joint_pos_lf;
  cmdTwoLegStandModeJointPos_.segment(2 * 3, 3) = des_joint_pos_rf;

  for (int i = 0; i < 2; i++) {
    cmdTwoLegStandModeTau_.segment(i * 6, 3) = Eigen::Vector3d::Zero();
    cmdTwoLegStandModeKp_[i * 6 + 2] = kp_calf_final_stage_;
    cmdTwoLegStandModeKp_[i * 6 + 1] = kp_thigh_final_stage_;
    cmdTwoLegStandModeKp_[i * 6] = kp_hip_final_stage_;

    cmdTwoLegStandModeKd_[i * 6 + 2] = kd_calf_final_stage_;
    cmdTwoLegStandModeKd_[i * 6 + 1] = kd_thigh_final_stage_;
    cmdTwoLegStandModeKd_[i * 6] = kd_hip_final_stage_;
  }
}

void LeggedController::runTwoLegStandRecoverStage(const ros::Time& time) {
  vector_t recoverPos = vector_t::Zero(12);
  recoverPos << 0, des_thigh_pos_recover_stage_, des_calf_pos_recover_stage_,
                0, des_thigh_pos_recover_stage_, des_calf_pos_recover_stage_,
                0, des_thigh_pos_recover_stage_, des_calf_pos_recover_stage_,
                0, des_thigh_pos_recover_stage_, des_calf_pos_recover_stage_;

  if(time.toSec() - enterTwoLegStandModeTime.toSec() <= recovery_duration_) {
    for (int i = 0; i < 12; i++) {
      cmdTwoLegStandModeKp_[i] = kp_recover_stage_;
      cmdTwoLegStandModeKd_[i] = kd_recover_stage_;
    }

    for(int i = 0; i < 12; i++) {
      cmdTwoLegStandModeJointPos_[i] = enterTwoLegStandModeJointPos_[i] + (recoverPos[i] - enterTwoLegStandModeJointPos_[i]) * (time.toSec() - enterTwoLegStandModeTime.toSec()) / recovery_duration_;
    }
    cmdTwoLegStandModeTau_.setZero();
  } else {
    if (std::abs(measuredRbdState_[2]) < 0.1 &&
        std::abs(measuredRbdState_[1]) < 0.1 &&
        checkJointPosNear(measuredRbdState_.segment<12>(6), recoverPos))
    {
      onExitTwoLegStandMode();
      printf("heihei\n");
    }
  }
}

void LeggedController::runTwoLegStandFinalStage(const ros::Time& time){
  switch (twoLegStandMode_)
  {
  case 0:
    runHappyNewYear(time);
    break;
  default:
    break;
  }
}

void LeggedController::onEnterBackFlipMode() {
  if(runningBackFlipMode) return;
  mpcRunning_ = false;
  runningBackFlipMode = true;
  runningPositionMode = true;
  enterBackFlipModeJointPos_ = jointPos_;
  backFlipModeCount_ = 0;
  curBackFlipModeTime = 0;
  firstInBackFlip_ = true;
}

void LeggedController::onExitBackFlipMode() {
  if(runningBackFlipMode) {
    mpcRunning_ = true;
    runningBackFlipMode = false;
    runningPositionMode = false;
    backFlipModeCount_ = 0;
    curBackFlipModeTime = 0;
    firstInBackFlip_ = false;
  }
}

bool LeggedController::initializeBackFlipMode() {
  if(!firstInBackFlip_) return true;
  double curTimeHere = backFlipModeCount_ * 0.002;
  if (curTimeHere < back_flip_adjust_duration_) {
    vector_t goalBackFlipAdjustJointPos = enterBackFlipModeJointPos_;
    goalBackFlipAdjustJointPos << 0, back_flip_adjust_thigh_pos_, back_flip_adjust_calf_pos_,
                                  0, back_flip_adjust_thigh_pos_, back_flip_adjust_calf_pos_,
                                  0, back_flip_adjust_thigh_pos_, back_flip_adjust_calf_pos_,
                                  0, back_flip_adjust_thigh_pos_, back_flip_adjust_calf_pos_;
    double phaseHere = curTimeHere / back_flip_adjust_duration_;
    cmdBackFlipModeJointPos_ = enterBackFlipModeJointPos_ + phaseHere * (goalBackFlipAdjustJointPos - enterBackFlipModeJointPos_);
    cmdBackFlipModeJointVel_ = vector_t::Zero(12);
    cmdBackFlipModeTau_ = vector_t::Zero(12);

    cmdBackFlipModeKp_ << kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_,
                          kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_,
                          kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_,
                          kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_, kp_adjust_stage_back_flip_;
    cmdBackFlipModeKd_ << kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_,
                          kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_,
                          kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_,
                          kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_, kd_adjust_stage_back_flip_;
    return false;
  }
  return true;
}

void LeggedController::runBackFlipMode(const ros::Time& time) {
  if(initializeBackFlipMode()) {
    if(firstInBackFlip_) {
      backFlipModeCount_ = 6;
      curBackFlipModeTime = 6 * 0.002;
      firstInBackFlip_ = false;
      backFlipCtrl_->FirstVisit(curBackFlipModeTime);
    }
    backFlipCtrl_->OneStep(curBackFlipModeTime, false, backFlipCmd_);

    for (int i = 0; i < 4; i++) {
      for (int j = 0; j < 3; j++) {
        cmdBackFlipModeKp_[i * 3 + j] = backFlipCmd_[i].kpJoint[j];
        cmdBackFlipModeKd_[i * 3 + j] = backFlipCmd_[i].kdJoint[j];
        cmdBackFlipModeJointPos_[i * 3 + j] = backFlipCmd_[i].qDes[j];
        cmdBackFlipModeJointVel_[i * 3 + j] = backFlipCmd_[i].qdDes[j];
        cmdBackFlipModeTau_[i * 3 + j] = backFlipCmd_[i].tauFeedForward[j];

        backFlipData_[i].q[j] = jointPos_[i * 3 + j];
        backFlipData_[i].qd[j] = jointVel_[i * 3 + j];
      }
    }

    if (backFlipCtrl_->EndOfPhase(backFlipData_)) {
      backFlipCtrl_->LastVisit();
    }
  }

  ++backFlipModeCount_;
  curBackFlipModeTime += 0.002;
}


void LeggedController::EmergencyStopCallback(const std_msgs::Float32::ConstPtr& msg) {
  emergency_flag = true;
}

void LeggedController::LoadControllerCallback(const std_msgs::Float32::ConstPtr& msg) {
  load_controller_flag = true;
}

void LeggedController::LoadStartCallback(const std_msgs::Float32::ConstPtr& msg) {
  stopping(ros::Time::now());
  starting(ros::Time::now());
}

//added for RL controller
bool LeggedController::loadModel(ros::NodeHandle &nh)
{
  ROS_INFO_STREAM("load student policy model");

  if (!nh.getParam("/policyModelPath_default", policyModelPath_) || !nh.getParam("/encoderModelPath_default", encoderModelPath_))
  {
      ROS_ERROR_STREAM("Get policy [default] path fail from param server, some error occur!");
      return false;
  }
  if (!nh.getParam("/policyModelPath_0", policyUpstairsModelPath_) || !nh.getParam("/encoderModelPath_0", encoderUpstairsModelPath_))
  {
      ROS_ERROR_STREAM("Get policy [0] path fail from param server, some error occur!");
      return false;
  }
  if (!nh.getParam("/policyModelPath1", policyModelPath1) )
        {
            ROS_ERROR_STREAM("Get stand policy path fail from param server, some error occur!");
            return false;
        }

  // create env
  onnxEnvPrt_.reset(new Ort::Env(ORT_LOGGING_LEVEL_WARNING, "LeggedOnnxController"));
  // create session
  Ort::SessionOptions sessionOptions;
  sessionOptions.SetIntraOpNumThreads(1);
  sessionOptions.SetInterOpNumThreads(1);
  Ort::AllocatorWithDefaultOptions allocator;

  //// running model load
  // policy session
  policySessionPtr_ = std::make_unique<Ort::Session>(*onnxEnvPrt_, policyModelPath_.c_str(), sessionOptions);
  policySessionPtr1_ = std::make_unique<Ort::Session>(*onnxEnvPrt_, policyModelPath1.c_str(), sessionOptions);
  policyInputNames_.clear();
  policyOutputNames_.clear();

  policyInputNames1_.clear();
  policyOutputNames1_.clear();

  policyInputShapes_.clear();
  policyOutputShapes_.clear();

  policyInputShapes1_.clear();
  policyOutputShapes1_.clear();

  for (int i = 0; i < policySessionPtr_->GetInputCount(); i++)
  {
      auto inputPolicynamePtr = policySessionPtr_->GetInputNameAllocated(i, allocator);
      inputPolicyNameAllocatedStrings.push_back(std::move(inputPolicynamePtr));
      policyInputNames_.push_back(inputPolicyNameAllocatedStrings.back().get());
      policyInputShapes_.push_back(policySessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = policySessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  /////// stand policy session
  for (int i = 0; i < policySessionPtr1_->GetInputCount(); i++)
  {
      auto inputPolicynamePtr = policySessionPtr1_->GetInputNameAllocated(i, allocator);
      inputPolicyNameAllocatedStrings1.push_back(std::move(inputPolicynamePtr));
      policyInputNames1_.push_back(inputPolicyNameAllocatedStrings1.back().get());
      // print name
      
      policyInputShapes1_.push_back(policySessionPtr1_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      
  }

  for (int i = 0; i < policySessionPtr_->GetOutputCount(); i++)
  {
      auto outputPolicynamePtr = policySessionPtr_->GetOutputNameAllocated(i, allocator);
      outputPolicyNameAllocatedStrings.push_back(std::move(outputPolicynamePtr));
      policyOutputNames_.push_back(outputPolicyNameAllocatedStrings.back().get());
      policyOutputShapes_.push_back(policySessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = policySessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  ////////// stand policy session
  for (int i = 0; i < policySessionPtr1_->GetOutputCount(); i++)
  {
      auto outputPolicynamePtr = policySessionPtr1_->GetOutputNameAllocated(i, allocator);
      outputPolicyNameAllocatedStrings1.push_back(std::move(outputPolicynamePtr));
      policyOutputNames1_.push_back(outputPolicyNameAllocatedStrings1.back().get());
      policyOutputShapes1_.push_back(policySessionPtr1_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      
  }
  // encoder session
  encoderSessionPtr_ = std::make_unique<Ort::Session>(*onnxEnvPrt_, encoderModelPath_.c_str(), sessionOptions);
  encoderInputNames_.clear();
  encoderOutputNames_.clear();
  encoderInputShapes_.clear();
  encoderOutputShapes_.clear();
  for (int i = 0; i < encoderSessionPtr_->GetInputCount(); i++)
  {
      auto inputEcodernamePtr = encoderSessionPtr_->GetInputNameAllocated(i, allocator);
      inputEncoderNameAllocatedStrings.push_back(std::move(inputEcodernamePtr));
      encoderInputNames_.push_back(inputEncoderNameAllocatedStrings.back().get());
      encoderInputShapes_.push_back(encoderSessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = encoderSessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  for (int i = 0; i < encoderSessionPtr_->GetOutputCount(); i++)
  {
      auto outputEcodernamePtr = encoderSessionPtr_->GetOutputNameAllocated(i, allocator);
      outputEncoderNameAllocatedStrings.push_back(std::move(outputEcodernamePtr));
      encoderOutputNames_.push_back(outputEncoderNameAllocatedStrings.back().get());
      //
      std::cout << "name:" << encoderOutputNames_[i] << std::endl;
      //
      encoderOutputShapes_.push_back(encoderSessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = encoderSessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  ROS_INFO_STREAM("Load Onnx model WALK successfully !!!");

  //// running upstair model load
  // policy session
  policyUpstairsSessionPtr_ = std::make_unique<Ort::Session>(*onnxEnvPrt_, policyUpstairsModelPath_.c_str(), sessionOptions);
  policyInputNames_.clear();
  policyOutputNames_.clear();
  policyInputShapes_.clear();
  policyOutputShapes_.clear();
  for (int i = 0; i < policyUpstairsSessionPtr_->GetInputCount(); i++)
  {
      auto inputPolicynamePtr = policyUpstairsSessionPtr_->GetInputNameAllocated(i, allocator);
      inputPolicyNameAllocatedStrings.push_back(std::move(inputPolicynamePtr));
      policyInputNames_.push_back(inputPolicyNameAllocatedStrings.back().get());
      policyInputShapes_.push_back(policyUpstairsSessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = policyUpstairsSessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  for (int i = 0; i < policyUpstairsSessionPtr_->GetOutputCount(); i++)
  {
      auto outputPolicynamePtr = policyUpstairsSessionPtr_->GetOutputNameAllocated(i, allocator);
      outputPolicyNameAllocatedStrings.push_back(std::move(outputPolicynamePtr));
      policyOutputNames_.push_back(outputPolicyNameAllocatedStrings.back().get());
      policyOutputShapes_.push_back(policyUpstairsSessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = policyUpstairsSessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  // encoder session
  encodeUpstairsSessionPtr_ = std::make_unique<Ort::Session>(*onnxEnvPrt_, encoderUpstairsModelPath_.c_str(), sessionOptions);
  encoderInputNames_.clear();
  encoderOutputNames_.clear();
  encoderInputShapes_.clear();
  encoderOutputShapes_.clear();
  for (int i = 0; i < encodeUpstairsSessionPtr_->GetInputCount(); i++)
  {
      auto inputEcodernamePtr = encodeUpstairsSessionPtr_->GetInputNameAllocated(i, allocator);
      inputEncoderNameAllocatedStrings.push_back(std::move(inputEcodernamePtr));
      encoderInputNames_.push_back(inputEncoderNameAllocatedStrings.back().get());
      encoderInputShapes_.push_back(encodeUpstairsSessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = encodeUpstairsSessionPtr_->GetInputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  for (int i = 0; i < encodeUpstairsSessionPtr_->GetOutputCount(); i++)
  {
      auto outputEcodernamePtr = encodeUpstairsSessionPtr_->GetOutputNameAllocated(i, allocator);
      outputEncoderNameAllocatedStrings.push_back(std::move(outputEcodernamePtr));
      encoderOutputNames_.push_back(outputEncoderNameAllocatedStrings.back().get());
      //
      std::cout << "name:" << encoderOutputNames_[i] << std::endl;
      //
      encoderOutputShapes_.push_back(encodeUpstairsSessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape());
      std::vector<int64_t> shape = encodeUpstairsSessionPtr_->GetOutputTypeInfo(i).GetTensorTypeAndShapeInfo().GetShape();
      std::cerr << "Shape: [";
      for (size_t j = 0; j < shape.size(); ++j)
      {
          std::cout << shape[j];
          if (j != shape.size() - 1)
          {
              std::cerr << ", ";
          }
      }
      std::cout << "]" << std::endl;
  }
  ROS_INFO_STREAM("Load Onnx model UPSTAIR successfully !!!");

  hidden_.assign(3 * 256, 0);
  cell_.assign(3 * 256, 0);
  lastActions_ = vector_t::Zero(12);
  rlPos_ = vector_t::Zero(12);
  return true;
}

bool LeggedController::loadRLCfg(ros::NodeHandle &nh)
{
  RLRobotCfg::InitState &initState = robotCfg_.initState;
  RLRobotCfg::ControlCfg &controlCfg = robotCfg_.controlCfg;
  RLRobotCfg::ObsScales &obsScales = robotCfg_.obsScales;

  int error = 0;
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/LF_HAA_joint", initState.LF_HAA_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/LF_HFE_joint", initState.LF_HFE_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/LF_KFE_joint", initState.LF_KFE_joint));

  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/RF_HAA_joint", initState.RF_HAA_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/RF_HFE_joint", initState.RF_HFE_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/RF_KFE_joint", initState.RF_KFE_joint));

  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/LH_HAA_joint", initState.LH_HAA_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/LH_HFE_joint", initState.LH_HFE_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/LH_KFE_joint", initState.LH_KFE_joint));

  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/RH_HAA_joint", initState.RH_HAA_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/RH_HFE_joint", initState.RH_HFE_joint));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/init_state/default_joint_angle/RH_KFE_joint", initState.RH_KFE_joint));

  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/control/stiffness", controlCfg.stiffness));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/control/damping", controlCfg.damping));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/control/stiffness_stand", controlCfg.stiffness_stand));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/control/damping_stand", controlCfg.damping_stand));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/control/action_scale", controlCfg.actionScale));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/control/decimation", controlCfg.decimation));

  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/normalization/clip_scales/clip_observations", robotCfg_.clipObs));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/normalization/clip_scales/clip_actions", robotCfg_.clipActions));

  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/normalization/obs_scales/lin_vel", obsScales.linVel));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/normalization/obs_scales/ang_vel", obsScales.angVel));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/normalization/obs_scales/dof_pos", obsScales.dofPos));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/normalization/obs_scales/dof_vel", obsScales.dofVel));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/normalization/obs_scales/height_measurements", obsScales.heightMeasurements));

  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/size/actions_size", actionsSize_));
  error += static_cast<int>(!nh.getParam("/LeggedRobotCfg/size/observations_size", observationSize_));

  actions_.resize(actionsSize_);
  realActions_.resize(actionsSize_);
  observations_.resize(observationSize_);

  command_.x = 0;
  command_.y = 0;
  command_.yaw = 0;
  lstcommand_.x = 0;
  lstcommand_.y = 0;
  lstcommand_.yaw = 0;
  baseLinVel_.setZero();
  basePosition_.setZero();
  std::vector<scalar_t> defaultJointAnglesRL{
      robotCfg_.initState.LF_HAA_joint, robotCfg_.initState.LF_HFE_joint, robotCfg_.initState.LF_KFE_joint,
      robotCfg_.initState.RF_HAA_joint, robotCfg_.initState.RF_HFE_joint, robotCfg_.initState.RF_KFE_joint,
      robotCfg_.initState.LH_HAA_joint, robotCfg_.initState.LH_HFE_joint, robotCfg_.initState.LH_KFE_joint,
      robotCfg_.initState.RH_HAA_joint, robotCfg_.initState.RH_HFE_joint, robotCfg_.initState.RH_KFE_joint};

  std::vector<scalar_t> defaultJointAnglesMPC{
      robotCfg_.initState.LF_HAA_joint, robotCfg_.initState.LF_HFE_joint, robotCfg_.initState.LF_KFE_joint,
      robotCfg_.initState.LH_HAA_joint, robotCfg_.initState.LH_HFE_joint, robotCfg_.initState.LH_KFE_joint,
      robotCfg_.initState.RF_HAA_joint, robotCfg_.initState.RF_HFE_joint, robotCfg_.initState.RF_KFE_joint,
      robotCfg_.initState.RH_HAA_joint, robotCfg_.initState.RH_HFE_joint, robotCfg_.initState.RH_KFE_joint};

  lastActions_.resize(actuatedDofNum_);
  defaultJointAnglesRL_.resize(actuatedDofNum_);
  defaultJointAnglesMPC_.resize(actuatedDofNum_);
  for (int i = 0; i < actuatedDofNum_; i++)
  {
      defaultJointAnglesRL_(i) = defaultJointAnglesRL[i];
      defaultJointAnglesMPC_(i) = defaultJointAnglesMPC[i];
      // std::cout<<" defaultJointAngles_ index: "<<defaultJointAngles_(i) <<std::endl;
  }


    return (error == 0);
  }

  void LeggedController::handleDefautMode()
  {
    for (int j = 0; j < hybridJointHandles_.size(); j++)
      hybridJointHandles_[j].setCommand(0, 0, 0, cfg_kd, 0);

    // ROS_INFO_STREAM(">>>>>>>>>enter RL Defaut Mode<<<<<<<<<<");

    // ROS_WARN("The value of kdConfig.cfg_kd is: %f", kdConfig.cfg_kd);
  }

  void LeggedController::handleLieMode()
  {
    if (standPercent_ <= 1)
    {
      for (int j = 0; j < hybridJointHandles_.size(); j++)
      {
        scalar_t pos_des = currentJointAngles_[j] * (1 - standPercent_) + lieJointAngles_[j] * standPercent_;
        hybridJointHandles_[j].setCommand(pos_des, 0, 50, 3, 0);

        // pos_des = currentJointAngles_[j] * (1 - standPercent_) + lieJointAngles_[j] * standPercent_;
        // hybridJointHandles_[j].setCommand(pos_des, 0, 350, 25, 0);
      }
      standPercent_ += 1 / standDuration_;
      standPercent_ = std::min(standPercent_, scalar_t(1));
    }
    // if(standPercent_ == 1 && switch_RL_flag && controller_switch_flag){
    //   switch_RL_flag = false;
    //   controller_switch_flag = false;
    //   gsmp_msgs::gl_quadbotCmd msg;
    //   msg.quadbot_kind = 1;
    //   msg.action_name = "getdowm";
    //   msg.action_id = 0;
    //   gsmp_quadbotCmd_pub_.publish(msg);
    // }
    // ROS_INFO_STREAM(">>>>>>>>>enter RL Lie Mode<<<<<<<<<<");
  }

  void LeggedController::handleStandMode()  
  {
    if (standPercent_ <= 1)
    {
      for (int j = 0; j < hybridJointHandles_.size(); j++)
      {
        // scalar_t pos_des = lieJointAngles_[j] * (1 - standPercent_) + standJointAngles_[j] * standPercent_;
        // hybridJointHandles_[j].setCommand(pos_des, 0, 350, 30, 0);
        // //
        scalar_t pos_des = currentJointAngles_[j] * (1 - standPercent_) + standJointAngles_[j] * standPercent_;
        hybridJointHandles_[j].setCommand(pos_des, 0, 50, 3, 0);
      }
      standPercent_ += 1 / standDuration_;
      standPercent_ = std::min(standPercent_, scalar_t(1));
      // std::cout << "adjust to mpc stand" << std::endl;
    }
    // if(standPercent_ == 1 && switch_RL_flag && controller_switch_flag){
    //   // switch_RL_flag = false;
    //   // controller_switch_flag = false;
    //   // leggedInterface_->getSwitchedModelReferenceManagerPtr()->setGaitType(0);
    //   mode_ = Mode::LIE;
    //   standPercent_ = 0;
    // }
    // ROS_INFO_STREAM(">>>>>>>>>enter RL Stand Mode<<<<<<<<<<");
  }

  void LeggedController::handleFreezeMode()
  {
    if (standPercent_ <= 1)
    {
      for (int j = 0; j < hybridJointHandles_.size(); j++)
      {
        // scalar_t pos_des = lieJointAngles_[j] * (1 - standPercent_) + standJointAngles_[j] * standPercent_;
        // hybridJointHandles_[j].setCommand(pos_des, 0, 350, 30, 0);
        // //
        scalar_t pos_des = currentJointAngles_[j] * (1 - standPercent_) + currentJointAngles_[j] * standPercent_;
        hybridJointHandles_[j].setCommand(pos_des, 0, 50, 3, 0);
      }
      standPercent_ += 1 / standDuration_;
      standPercent_ = std::min(standPercent_, scalar_t(1));
    }
    // ROS_INFO_STREAM(">>>>>>>>>enter RL Freeze Mode<<<<<<<<<<");
  }

  void LeggedController::handleWalkMode()
  {
      // compute observation & actions
      if (loopCount_ % robotCfg_.controlCfg.decimation == 0)
      {
          // ROS_INFO_STREAM(">>>>>>>>>enter RL Walk Mode<<<<<<<<<<");
          computeObservation();
          if(RLType == 0) computeActions();
          if(RLType == 1) computeUpstairActions();
          // limit action rang
          scalar_t actionMin = -robotCfg_.clipActions;
          scalar_t actionMax = robotCfg_.clipActions;
          std::transform(realActions_.begin(), realActions_.end(), realActions_.begin(),
                          [actionMin, actionMax](scalar_t x)
                          { return std::max(actionMin, std::min(actionMax, x)); });

          std::transform(actions_.begin(), actions_.end(), actions_.begin(),
                          [actionMin, actionMax](scalar_t x)
                          { return std::max(actionMin, std::min(actionMax, x)); });
      }

      vector_t rl_pos_data;
      rl_pos_data = vector_t::Zero(12);

      // set action
      for (int i = 0; i < actionsSize_; i++)
      {
          if (i == 0 || i == 3 || i == 6 || i == 9)
          {
              std::string partName = hybridJointHandles_[i].getName();

              scalar_t pos_des = (realActions_[i] * robotCfg_.controlCfg.actionScale[partName] + defaultJointAnglesMPC_(i));
              double stiffness = robotCfg_.controlCfg.stiffness[partName]; // 根据关节名称获取刚度
              double damping = robotCfg_.controlCfg.damping[partName];     // 根据关节名称获取阻尼
              // std::cout << "action_scale:" << robotCfg_.controlCfg.actionScale[partName] << std::endl;
              // std::cout << "第" << i << "关节：" << partName << " kp:" << stiffness << " kd:" << damping <<" dafault:"<<defaultJointAnglesMPC_(i)<< std::endl;


              /////////output  torque limit
              double torque = hybridJointHandles_[i].getFeedforward() +
                         stiffness * (pos_des - hybridJointHandles_[i].getPosition());
              double pos_real = pos_des;
              double torque_limit = 20;
              if(torque > torque_limit) {
                pos_real = ((torque_limit - hybridJointHandles_[i].getFeedforward()) / stiffness) + hybridJointHandles_[i].getPosition();
              }
              else if(torque < -torque_limit) {
                pos_real = ((-torque_limit - hybridJointHandles_[i].getFeedforward()) / stiffness) + hybridJointHandles_[i].getPosition();
              }
              // std::cout << "joint" << i << ", pos_des:" << pos_des << ",pos_real:" << pos_real << std::endl;
              hybridJointHandles_[i].setCommand(pos_real, 0, stiffness, damping, 0);
              ////////////output  torque limit

              // std::cout << "action:" << actions_[i] << std::endl;
              lastActions_(i, 0) = actions_[i];

              rl_pos_data(i) = pos_real;
              // std::cout << "rl_pos_data[" << i << "]:" << rl_pos_data[i] << ",stiffness:" << stiffness << ",damping:" << damping << std::endl;
          }
          else
          {
              std::string partName = hybridJointHandles_[i].getName();
              scalar_t pos_des = realActions_[i] * robotCfg_.controlCfg.actionScale[partName] + defaultJointAnglesMPC_(i);
              double stiffness = robotCfg_.controlCfg.stiffness[partName]; // 根据关节名称获取刚度
              double damping = robotCfg_.controlCfg.damping[partName];     // 根据关节名称获取阻尼
              // std::cout << "第" << i << "关节：" << partName << " kp:" << stiffness << " kd:" << damping << std::endl;
              // std::cout << "num" << i << "action_scale:" << robotCfg_.controlCfg.actionScale[partName] << std::endl;

              /////////output  torque limit
              double torque = hybridJointHandles_[i].getFeedforward() +
                         stiffness * (pos_des - hybridJointHandles_[i].getPosition());
              double pos_real = pos_des;
              double torque_limit = 20;
              if(torque > torque_limit) {
                pos_real = ((torque_limit - hybridJointHandles_[i].getFeedforward()) / stiffness) + hybridJointHandles_[i].getPosition();
              }
              else if(torque < -torque_limit) {
                pos_real = ((-torque_limit - hybridJointHandles_[i].getFeedforward()) / stiffness) + hybridJointHandles_[i].getPosition();
              }
              //std::cout << "joint" << i << ", pos_des:" << pos_des << ",pos_real:" << pos_real << std::endl;
              hybridJointHandles_[i].setCommand(pos_real, 0, stiffness, damping, 0);
              ////////////output  torque limit
              
              lastActions_(i, 0) = actions_[i];

              rl_pos_data(i) = pos_real;
              // std::cout << "rl_pos_data[" << i << "]:" << rl_pos_data[i] << ",stiffness:" << stiffness << ",damping:" << damping << std::endl;
          }
      }
      rlPosPublisher_.publish(createFloat64MultiArrayFromVector(rl_pos_data));
  }

  void LeggedController::handleRlstandMode()
    {
        // compute observation & actions
        if (loopCount_ % robotCfg_.controlCfg.decimation == 0)
        {
            computeObservation();
            computeActions1();
            // limit action range
            scalar_t actionMin = -robotCfg_.clipActions;
            scalar_t actionMax = robotCfg_.clipActions;
            std::transform(actions_.begin(), actions_.end(), actions_.begin(),
                           [actionMin, actionMax](scalar_t x)
                           { return std::max(actionMin, std::min(actionMax, x)); });
        }
        // set action
        for (int i = 0; i < actionsSize_; i++)
        {
            if (i == 0 || i == 3 || i == 6 || i == 9)
            {
                std::string partName = hybridJointHandles_[i].getName();

                scalar_t pos_des = realActions_[i] * robotCfg_.controlCfg.actionScale[partName] + defaultJointAnglesMPC_(i);
                double stiffness = robotCfg_.controlCfg.stiffness_stand[partName]; // 根据关节名称获取刚度
                double damping = robotCfg_.controlCfg.damping_stand[partName];     // 根据关节名称获取阻尼
                
                hybridJointHandles_[i].setCommand(pos_des, 0, stiffness, damping, 0);

                // std::cout << "action:" << actions_[i] << std::endl;
                lastActions_(i, 0) = actions_[i];
            }
            else
            {
                std::string partName = hybridJointHandles_[i].getName();
                scalar_t pos_des = realActions_[i] * robotCfg_.controlCfg.actionScale[partName] + defaultJointAnglesMPC_(i);
                double stiffness = robotCfg_.controlCfg.stiffness_stand[partName]; // 根据关节名称获取刚度
                double damping = robotCfg_.controlCfg.damping_stand[partName];     // 根据关节名称获取阻尼
                
                hybridJointHandles_[i].setCommand(pos_des, 0, stiffness, damping, 0);
                lastActions_(i, 0) = actions_[i];
            }
        }
        
    }

  void LeggedController::computeObservation()
  {

      // command
      static vector3_t command(0);
      ros::Time time_now = ros::Time::now();
      double des_x = command_.x;
      double des_y = command_.y;
      double des_yaw = command_.yaw;

      if(time_now - cmdTime < ros::Duration(0.1)) {  //cmd_vel　control
        // command[0] = fabs(des_x - command[0]) > dt_rl_xyz(0) && (des_x==0)? (command[0] + dt_rl_xyz(0)*((des_x - command[0])/fabs(des_x - command[0]))) : des_x;
        // command[1] = fabs(des_y - command[1]) > dt_rl_xyz(1)&&(des_x==0)? (command[1] + dt_rl_xyz(1)*((des_y - command[1])/fabs(des_y - command[1]))) : des_y;
        // command[2] = fabs(des_yaw - command[2]) > dt_rl_xyz(2)&&(des_x==0)? (command[2] + dt_rl_xyz(2)*((des_yaw - command[2])/fabs(des_yaw - command[2]))) : des_yaw;
        
        command[0] =  command_.x;
        command[1] = command_.y;
        command[2] = command_.yaw;
        // std::cout << "the x1 vel is :" << command[0] <<" "<< command[1]<<" "<< command[2]<< std::endl; 

      }else if(isPassiveTrot_.load()){
        command[0] =  desVelXYZRl[0];
        command[1] = desVelXYZRl[1];
        command[2] = desVelXYZRl[2];


      }
      
      else {
        // command[0] = fabs(command[0]) - dt_rl_xyz(0)>0? command[0]/fabs(command[0]) * (fabs(command[0]) - dt_rl_xyz(0)) : 0;
        // command[1] = fabs(command[1]) - dt_rl_xyz(1)>0? command[1]/fabs(command[1]) * (fabs(command[1]) - dt_rl_xyz(1)) : 0;;
        // command[2] = fabs(command[2]) - dt_rl_xyz(2)>0? command[2]/fabs(command[2]) * (fabs(command[2]) - dt_rl_xyz(2)) : 0;;
        command[0] =  0;
        command[1] = 0;
        command[2] = 0;
        // std::cout << "the x2 vel is :" << command[0] <<" "<< command[1]<<" "<< command[2]<< std::endl; 

      }
      // std::cout << "the x 2vel is :" << command[0] << std::endl; 

      double thresh = 0.3;
      double decay = 0.02;
      if (fabs(command[0] - lstcommand_.x) > thresh) {
        command[0] = lstcommand_.x + (command[0]- lstcommand_.x) * decay;
        // std::cout << "command[0]:" << command[0] << std::endl;
      }
      lstcommand_.x = command[0];


      // actions
      vector_t actions(lastActions_);

      RLRobotCfg::ObsScales &obsScales = robotCfg_.obsScales;
      matrix_t commandScaler = Eigen::DiagonalMatrix<scalar_t, 3>(obsScales.linVel, obsScales.linVel, obsScales.angVel);

      vector_t obs(observationSize_);
      // clang-format off
      obs << propri_.projectedGravity,
          propri_.baseAngVel * obsScales.angVel,
          (propri_.jointPos - defaultJointAnglesRL_) * obsScales.dofPos,
          propri_.jointVel * obsScales.dofVel,
          actions,
          commandScaler * command;
      // clang-format on

      for (size_t i = 0; i < obs.size(); i++)
          observations_[i] = static_cast<tensor_element_t>(obs(i));
      // Limit observation range
      scalar_t obsMin = -robotCfg_.clipObs;
      scalar_t obsMax = robotCfg_.clipObs;
      std::transform(observations_.begin(), observations_.end(), observations_.begin(),
                      [obsMin, obsMax](scalar_t x)
                      { return std::max(obsMin, std::min(obsMax, x)); });
  }

  void LeggedController::computeActions()
  {
      // Prepare encoder input
      Ort::MemoryInfo memoryInfo = Ort::MemoryInfo::CreateCpu(OrtAllocatorType::OrtArenaAllocator, OrtMemType::OrtMemTypeDefault);
      std::vector<Ort::Value> encoderInputValues;
      encoderInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, observations_.data(), observations_.size(), encoderInputShapes_[0].data(), encoderInputShapes_[0].size()));
      encoderInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, hidden_.data(), hidden_.size(), encoderInputShapes_[1].data(), encoderInputShapes_[1].size()));
      encoderInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, cell_.data(), cell_.size(), encoderInputShapes_[2].data(), encoderInputShapes_[2].size()));
      // Run encoder inference
      Ort::RunOptions runOptions;
      std::vector<Ort::Value> encoderOutputValues = encoderSessionPtr_->Run(runOptions, encoderInputNames_.data(), encoderInputValues.data(), 3, encoderOutputNames_.data(), 3);
      std::vector<tensor_element_t> encoderOutput(encoderOutputShapes_[0][1]); // Assuming output shape [batch, output_dim]
      std::copy(encoderOutputValues[0].GetTensorMutableData<tensor_element_t>(), encoderOutputValues[0].GetTensorMutableData<tensor_element_t>() + encoderOutput.size(), encoderOutput.begin());
      std::copy(encoderOutputValues[1].GetTensorMutableData<tensor_element_t>(), encoderOutputValues[1].GetTensorMutableData<tensor_element_t>() + hidden_.size(), hidden_.begin());
      std::copy(encoderOutputValues[2].GetTensorMutableData<tensor_element_t>(), encoderOutputValues[2].GetTensorMutableData<tensor_element_t>() + cell_.size(), cell_.begin());
      // Merge encoder output with original observations
      std::vector<tensor_element_t> mergedInput(observations_.size() + encoderOutput.size());
      std::copy(observations_.begin(), observations_.end(), mergedInput.begin());
      std::copy(encoderOutput.begin(), encoderOutput.end(), mergedInput.begin() + observations_.size());

      // Prepare policy model input
      std::vector<Ort::Value> policyInputValues;
      Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, mergedInput.data(), mergedInput.size(), policyInputShapes_[0].data(), policyInputShapes_[0].size());
      policyInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, mergedInput.data(), mergedInput.size(), policyInputShapes_[0].data(), policyInputShapes_[0].size()));

      // Run policy inference
      std::vector<Ort::Value> policyOutputValues = policySessionPtr_->Run(runOptions, policyInputNames_.data(), policyInputValues.data(), 1, policyOutputNames_.data(), 1);

      // Extract and store actions
      for (int i = 0; i < actions_.size(); i++)
      {
          actions_[i] = *(policyOutputValues[0].GetTensorMutableData<tensor_element_t>() + i);
      }

      realActions_ = actions_;
      // MPC ORDER
      realActions_[3] = actions_[6];
      realActions_[4] = actions_[7];
      realActions_[5] = actions_[8];
      realActions_[6] = actions_[3];
      realActions_[7] = actions_[4];
      realActions_[8] = actions_[5];
  }

  ////// stand action cal
  void LeggedController::computeActions1()
  {
      // Prepare encoder input
      Ort::MemoryInfo memoryInfo = Ort::MemoryInfo::CreateCpu(OrtAllocatorType::OrtArenaAllocator, OrtMemType::OrtMemTypeDefault);
      
      
      // // Run encoder inference
      Ort::RunOptions runOptions;
      
      std::vector<Ort::Value> policyInputValues1;
      
      policyInputValues1.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, observations_.data(), observations_.size(), policyInputShapes1_[0].data(), policyInputShapes1_[0].size()));
      // std::cout<< "policy_size:" <<policyInputShapes_[0].data()<<std::endl; 
      // std::cout<< "policy_size:" <<policyInputShapes_[0][0]<<std::endl;
      // std::cout<< "policy_size:" <<policyInputShapes_[0][1]<<std::endl;

      // Run policy inference
      std::vector<Ort::Value> policyOutputValues1;
      policyOutputValues1 = policySessionPtr1_->Run(runOptions, policyInputNames1_.data(), policyInputValues1.data(), 1, policyOutputNames1_.data(), 1);
      
      // Extract and store actions
      for (int i = 0; i < actions_.size(); i++)
      {
          actions_[i] = *(policyOutputValues1[0].GetTensorMutableData<tensor_element_t>() + i);
      }

      realActions_ = actions_;
      // MPC ORDER
      realActions_[3] = actions_[6];
      realActions_[4] = actions_[7];
      realActions_[5] = actions_[8];
      realActions_[6] = actions_[3];
      realActions_[7] = actions_[4];
      realActions_[8] = actions_[5];
  }

  void LeggedController::computeUpstairActions()
  {

      // std::cout << "Upstair actions" << std::endl;
      // Prepare encoder input
      Ort::MemoryInfo memoryInfo = Ort::MemoryInfo::CreateCpu(OrtAllocatorType::OrtArenaAllocator, OrtMemType::OrtMemTypeDefault);
      std::vector<Ort::Value> encoderInputValues;
      encoderInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, observations_.data(), observations_.size(), encoderInputShapes_[0].data(), encoderInputShapes_[0].size()));
      encoderInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, hidden_.data(), hidden_.size(), encoderInputShapes_[1].data(), encoderInputShapes_[1].size()));
      encoderInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, cell_.data(), cell_.size(), encoderInputShapes_[2].data(), encoderInputShapes_[2].size()));
      // Run encoder inference
      Ort::RunOptions runOptions;
      std::vector<Ort::Value> encoderOutputValues = encodeUpstairsSessionPtr_->Run(runOptions, encoderInputNames_.data(), encoderInputValues.data(), 3, encoderOutputNames_.data(), 3);
      std::vector<tensor_element_t> encoderOutput(encoderOutputShapes_[0][1]); // Assuming output shape [batch, output_dim]
      std::copy(encoderOutputValues[0].GetTensorMutableData<tensor_element_t>(), encoderOutputValues[0].GetTensorMutableData<tensor_element_t>() + encoderOutput.size(), encoderOutput.begin());
      std::copy(encoderOutputValues[1].GetTensorMutableData<tensor_element_t>(), encoderOutputValues[1].GetTensorMutableData<tensor_element_t>() + hidden_.size(), hidden_.begin());
      std::copy(encoderOutputValues[2].GetTensorMutableData<tensor_element_t>(), encoderOutputValues[2].GetTensorMutableData<tensor_element_t>() + cell_.size(), cell_.begin());
      // Merge encoder output with original observations
      std::vector<tensor_element_t> mergedInput(observations_.size() + encoderOutput.size());
      std::copy(observations_.begin(), observations_.end(), mergedInput.begin());
      std::copy(encoderOutput.begin(), encoderOutput.end(), mergedInput.begin() + observations_.size());

      // Prepare policy model input
      std::vector<Ort::Value> policyInputValues;
      Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, mergedInput.data(), mergedInput.size(), policyInputShapes_[0].data(), policyInputShapes_[0].size());
      policyInputValues.push_back(Ort::Value::CreateTensor<tensor_element_t>(memoryInfo, mergedInput.data(), mergedInput.size(), policyInputShapes_[0].data(), policyInputShapes_[0].size()));

      // Run policy inference
      std::vector<Ort::Value> policyOutputValues = policyUpstairsSessionPtr_->Run(runOptions, policyInputNames_.data(), policyInputValues.data(), 1, policyOutputNames_.data(), 1);

      // Extract and store actions
      for (int i = 0; i < actions_.size(); i++)
      {
          actions_[i] = *(policyOutputValues[0].GetTensorMutableData<tensor_element_t>() + i);
      }

      realActions_ = actions_;
      realActions_[3] = actions_[6];
      realActions_[4] = actions_[7];
      realActions_[5] = actions_[8];
      realActions_[6] = actions_[3];
      realActions_[7] = actions_[4];
      realActions_[8] = actions_[5];
  }

  void LeggedController::cmdVelCallback(const geometry_msgs::Twist &msg)
  {
    command_.x = msg.linear.x;
    command_.y = msg.linear.y;
    command_.yaw = msg.angular.z;
    cmdTime = ros::Time::now();
  }

    //controller_switch
  void LeggedController::ControllerSwitchCallback(const std_msgs::Float32::ConstPtr& msg) {
    ros::Duration t(0.2);
    if (ros::Time::now() - switchTime > t)
    {
      if (!controller_switch_flag){
        ROS_INFO_STREAM("Controller switched to RL controller");
        controller_switch_flag = !controller_switch_flag;
        for (size_t i = 0; i < hybridJointHandles_.size(); i++)
        {
          currentJointAngles_[i] = hybridJointHandles_[i].getPosition();
        }
        mode_ = Mode::WALK;
        ros::param::set("/robotState/controller_switch_flag","1");
      }else {
        ROS_INFO_STREAM("Controller switched to MPC controller");
        // if(mode_ == Mode::LIE){
        //   gsmp_msgs::gl_quadbotCmd msg;
        //   msg.quadbot_kind = 1;
        //   msg.action_name = "getdowm";
        //   msg.action_id = 0;
        //   gsmp_quadbotCmd_pub_.publish(msg);
        //   controller_switch_flag = !controller_switch_flag;
        // }else if(mode_ == Mode::WALK){
        //   mode_ = Mode::STAND;
        //   switch_RL_flag = true;
        //   standPercent_ = 0;
        // }else{
        //   // leggedInterface_->getSwitchedModelReferenceManagerPtr()->setGaitType(0);  //oneshot
        // }
        controller_switch_flag = !controller_switch_flag; 
        leggedInterface_->getSwitchedModelReferenceManagerPtr()->setGaitType(0);  //oneshot
        ros::param::set("/robotState/controller_switch_flag","0");

        updateStateEstimation(ros::Time::now(),ros::Duration(0.002));
        currentObservation_.input.setZero(leggedInterface_->getCentroidalModelInfo().inputDim);
        TargetTrajectories target_trajectories({currentObservation_.time}, {currentObservation_.state}, {currentObservation_.input});
        currentObservation_.mode = ModeNumber::STANCE;
        mpcMrtInterface_->getReferenceManager().setTargetTrajectories(target_trajectories);
      }
      switchTime = ros::Time::now();
    }
    mpcRunning_ = controller_switch_flag? false : true;
  }

  //stair mode sub
  void LeggedController::StairSwitchCallback(const gsmp_msgs::gl_stairmodeCmd& msg)
  {
    //ROS_INFO("I heard: [%s]", msg->data.c_str());
    ROS_INFO("Data of stair are %d, %0.3f, %0.3f, %0.3f, %0.3f", msg.classification, 
                                                                  msg.depth,
                                                                  msg.x_coord,
                                                                  msg.y_coord,
                                                                  msg.z_coord );
    //classification: 0

    ros::Duration t(0.2);
    if (ros::Time::now() - switchTime > t && controller_switch_flag)
    {
      if (mode_ != Mode::LIE)
      {
        mode_ = Mode::UPSTAIR;
        RLType = 1;
        ROS_INFO("switched To UPSTAIR");
      }
      switchTime = ros::Time::now();
    }
  }


  void LeggedController::rlPassiveMode(){

    double filterdVel[3] = {0, 0, 0};

    est_vel_local_rl_.updateFromBuffer();
    auto est_vel_origin = est_vel_local_rl_.get();
    // rot_mat_buffer_rl_.updateFromBuffer();
    // auto rot_mat = rot_mat_buffer_rl_.get();

    filterdVel[0] = lowPassFilterX_.update(est_vel_origin[0]);
    filterdVel[1] = lowPassFilterY_.update(est_vel_origin[1]);
    filterdVel[2] = lowPassFilterYaw_.update(est_vel_origin[2]);

    double minVelX = -0.6;        //x: -0.6 y: -0.4 yaw: -0.8
    double maxVelX = 0.8;         //x: 1.2 y: 0.4 yaw: 0.8
    double zeroTreshX = 0.14;
    double accTreshX = 0.1;       // y: 0.2 yaw: 0.5
    double filterVelX = 0.95;
    double velTrigerX = 0.7;    //速度继续滤波的阈值，大于0.01继续滤波
    double velHoldX = maxVelX;  

    double desVelX = 1.1 * leggedInterface_->getSwitchedModelReferenceManagerPtr()->WalkDogModeXYVelGen(filterdVel[0], 0, minVelX, maxVelX, zeroTreshX, accTreshX, filterVelX, velTrigerX, velHoldX);

    double minVelY = -0.6;        // y: -0.4 yaw: -0.8
    double maxVelY = 0.6;         // y: 0.4 yaw: 0.8
    double zeroTreshY = 0.12;
    double accTreshY = 0.1;       // y: 0.2 yaw: 0.5
    double filterVelY = 0.95;
    double velTrigerY = 0.0;
    double velHoldY = 0;
    double desVelY = 1.1 * leggedInterface_->getSwitchedModelReferenceManagerPtr()->WalkDogModeXYVelGen(filterdVel[1], 0, minVelY, maxVelY, zeroTreshY, accTreshY, filterVelY, velTrigerY, velHoldY);


    double errVx = 0 - filterdVel[0];
    double errVy = 0 - filterdVel[1];
    double cmdVelYaw = 0;
    double desVelYaw = leggedInterface_->getSwitchedModelReferenceManagerPtr()->WalkDogModeYawVelGen(filterdVel[2], cmdVelYaw, errVx, errVy, -0.82, 0.82, 0.08, 0.5, 40);

    desVelXYZRl[0] = desVelX;
    desVelXYZRl[1] = desVelY;
    desVelXYZRl[2] = desVelYaw;


    std::cout<<" desVelXYZRl:"<<desVelXYZRl.transpose()<<std::endl;



  }


  // roll-recovery 
  void LeggedController::bodyAdjustmentAndRecovery(const SystemObservation& observation,const ros::Time& time){
    // scalar_t rollLast = observation.state(11);
    // std::cout<<" rollLast:"<<rollLast<<std::endl;
    // if(rollLast > M_PI_4 || rollLast < -M_PI_4){
    if(true){
      // std::cout<<" ************bodyAdjustmentAndRecovery -> body flip************"<<std::endl;
      // std::cout<<" recoveryCount_:"<<recoveryCount_<<std::endl;
      //   std::cout<<" curRecoveryState:"<<curRecoveryState<<std::endl;
      if(recoveryCount_==0){
        curRecoveryState++;
        switch(curRecoveryState){
          case 1:
            recoveryTargetPos = shrinkTargetPos;
            kp_recovery_mode_ = kpRecovery_;
            kd_recovery_mode_ = kdRecovery_;
            recoveryStateStartTime = time;
            recoveryCurPos = observation.state.segment<12>(12);
            recoveryTime_ = recoveryStageTime(curRecoveryState-1);
            break;
          case 2:
            recoveryTargetPos = straightenTargetPos;
            kp_recovery_mode_ = kpRecovery_;
            kd_recovery_mode_ = kdRecovery_;
            recoveryStateStartTime = time;
            // recoveryCurPos = observation.state.segment<12>(12);
            recoveryCurPos = shrinkTargetPos;
            recoveryTime_ = recoveryStageTime(curRecoveryState-1);
            break;
          case 3:
            recoveryTargetPos = reverseStage1TargetPos;
            kp_recovery_mode_ = kpRecovery_;
            kd_recovery_mode_ = kdRecovery_;
            recoveryStateStartTime = time;
            // recoveryCurPos = observation.state.segment<12>(12);
            recoveryCurPos = straightenTargetPos;
            recoveryTime_ = recoveryStageTime(curRecoveryState-1);
            break;
          case 4:
            recoveryTargetPos = reverseStage2TargetPos;
            kp_recovery_mode_ = kpRecovery_;
            kd_recovery_mode_ = kdRecovery_;
            recoveryStateStartTime = time;
            // recoveryCurPos = observation.state.segment<12>(12);
            recoveryCurPos = reverseStage1TargetPos;
            recoveryTime_ = recoveryStageTime(curRecoveryState-1);
            break;
          case 5:
            recoveryTargetPos = reverseStage3TargetPos;
            kp_recovery_mode_ = kpRecovery_;
            kd_recovery_mode_ = kdRecovery_;
            recoveryStateStartTime = time;
            // recoveryCurPos = observation.state.segment<12>(12);
            recoveryCurPos = reverseStage2TargetPos;
            recoveryTime_ = recoveryStageTime(curRecoveryState-1);
            break;
          case 6:
            recoveryTargetPos = reverseStage4TargetPos;  
            kp_recovery_mode_ = kpRecovery_;
            kd_recovery_mode_ = kdRecovery_;
            recoveryStateStartTime = time;
            // recoveryCurPos = observation.state.segment<12>(12);
            recoveryCurPos = reverseStage3TargetPos;
            recoveryTime_ = recoveryStageTime(curRecoveryState-1);
            break;  
          case 7:
            recoveryTargetPos = reverseStage5TargetPos;  
            kp_recovery_mode_ = kpRecovery_;
            kd_recovery_mode_ = kdRecovery_;
            recoveryStateStartTime = time;
            // recoveryCurPos = observation.state.segment<12>(12);
            recoveryCurPos = reverseStage4TargetPos;
            recoveryTime_ = recoveryStageTime(curRecoveryState-1);
            break;
          default:
            recoveryTargetPos = observation.state.segment<12>(12);  
            recoveryCurPos = observation.state.segment<12>(12);
            recoveryStateStartTime = time;
            kp_recovery_mode_ = 0;
            kd_recovery_mode_ = 1;

            curRecoveryState = 0;
            // test
            if(false){
              rollRecoveryMode = false;
              starting(time);
            }
            break;
        }
        // recoveryStateStartTime = time;
        // recoveryCurPos = observation.state.segment<12>(12);
        // std::cout<<" set target pos"<<std::endl;

      }
      recoveryCount_++;

    }
    else{
      std::cout<<" ************bodyAdjustmentAndRecovery -> recovery************"<<std::endl;
    }
    // recoveryCount_++;


    
  }



}  // namespace legged

PLUGINLIB_EXPORT_CLASS(legged::LeggedController, controller_interface::ControllerBase)
PLUGINLIB_EXPORT_CLASS(legged::LeggedCheaterController, controller_interface::ControllerBase)
