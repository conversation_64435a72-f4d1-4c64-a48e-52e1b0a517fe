[  0%] Built target tf2_msgs_generate_messages_py
[  0%] Built target roscpp_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_eus
[  0%] Built target roscpp_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_eus
[  0%] Built target roscpp_generate_messages_py
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target rosgraph_msgs_generate_messages_py
[  0%] Built target rosgraph_msgs_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_nodejs
[  0%] Built target ocs2_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target visualization_msgs_generate_messages_py
[  0%] Built target ocs2_msgs_generate_messages_eus
[  0%] Built target ocs2_msgs_generate_messages_nodejs
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target ocs2_msgs_generate_messages_py
[  0%] Built target ocs2_msgs_generate_messages_lisp
[  0%] Built target visualization_msgs_generate_messages_nodejs
[  0%] Built target visualization_msgs_generate_messages_lisp
[  0%] Built target visualization_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target visualization_msgs_generate_messages_eus
[  0%] Built target actionlib_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target actionlib_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target actionlib_generate_messages_py
[  0%] Built target actionlib_generate_messages_lisp
[  0%] Built target actionlib_generate_messages_nodejs
[  0%] Built target actionlib_msgs_generate_messages_cpp
[  0%] Built target actionlib_msgs_generate_messages_nodejs
[  0%] Built target actionlib_msgs_generate_messages_eus
[  0%] Built target tf2_msgs_generate_messages_eus
[  0%] Built target _catkin_empty_exported_target
[  0%] Built target actionlib_msgs_generate_messages_py
[  0%] Built target tf2_msgs_generate_messages_cpp
[  0%] Built target actionlib_msgs_generate_messages_lisp
[  0%] Built target tf2_msgs_generate_messages_lisp
[  0%] Built target tf2_msgs_generate_messages_nodejs
[100%] Built target ocs2_self_collision_visualization
