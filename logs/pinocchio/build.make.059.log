/usr/local/bin/cmake -S/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/pinocchio -B/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio --check-build-system CMakeFiles/Makefile.cmake 0
/usr/local/bin/cmake -E cmake_progress_start /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio/CMakeFiles /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio//CMakeFiles/progress.marks
/usr/bin/make  -f CMakeFiles/Makefile2 all
make[1]: 进入目录“/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio”
/usr/bin/make  -f src/CMakeFiles/pinocchio.dir/build.make src/CMakeFiles/pinocchio.dir/depend
make[2]: 进入目录“/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio”
cd /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio && /usr/local/bin/cmake -E cmake_depends "Unix Makefiles" /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/pinocchio /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/src/pinocchio/src /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio/src /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio/src/CMakeFiles/pinocchio.dir/DependInfo.cmake "--color="
make[2]: 离开目录“/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio”
/usr/bin/make  -f src/CMakeFiles/pinocchio.dir/build.make src/CMakeFiles/pinocchio.dir/build
make[2]: 进入目录“/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio”
make[2]: 对“src/CMakeFiles/pinocchio.dir/build”无需做任何事。
make[2]: 离开目录“/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio”
[100%] Built target pinocchio
make[1]: 离开目录“/media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio”
/usr/local/bin/cmake -E cmake_progress_start /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/pinocchio/CMakeFiles 0
