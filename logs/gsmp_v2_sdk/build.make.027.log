[  4%] [32mBuilding CXX object third-party/serial/CMakeFiles/serial.dir/src/serial.cc.o[0m
[  9%] [32mBuilding CXX object third-party/serial/CMakeFiles/serial.dir/src/impl/list_ports/list_ports_linux.cc.o[0m
[ 13%] [32mBuilding CXX object third-party/serial/CMakeFiles/serial.dir/src/impl/unix.cc.o[0m
[ 22%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercateoe.c.o[0m
[ 22%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatbase.c.o[0m
[ 31%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatconfig.c.o[0m
[ 31%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatcoe.c.o[0m
[ 36%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatdc.c.o[0m
[ 40%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatfoe.c.o[0m
[ 45%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatmain.c.o[0m
[ 50%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatprint.c.o[0m
[ 54%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/soem/ethercatsoe.c.o[0m
[ 59%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/oshw/linux/oshw.c.o[0m
[ 63%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/oshw/linux/nicdrv.c.o[0m
[ 68%] [32mBuilding C object third-party/soem/CMakeFiles/soem.dir/osal/linux/osal.c.o[0m
[ 72%] [32m[1mLinking C shared library libsoem.so[0m
[ 72%] Built target soem
[ 77%] [32m[1mLinking CXX shared library libserial.so[0m
[ 77%] Built target serial
[ 81%] [32mBuilding CXX object CMakeFiles/gsmp_v2_sdk.dir/src/EtherCatMaster_2.cpp.o[0m
[ 86%] [32mBuilding CXX object CMakeFiles/gsmp_v2_sdk.dir/src/MiMotor_2.cpp.o[0m
[ 90%] [32mBuilding CXX object CMakeFiles/gsmp_v2_sdk.dir/src/MotorUtils.cpp.o[0m
[ 95%] [32mBuilding CXX object CMakeFiles/gsmp_v2_sdk.dir/src/MotorBase_2.cpp.o[0m
[100%] [32m[1mLinking CXX shared library /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/gsmp_v2_sdk/lib/libgsmp_v2_sdk.so[0m
[100%] Built target gsmp_v2_sdk
