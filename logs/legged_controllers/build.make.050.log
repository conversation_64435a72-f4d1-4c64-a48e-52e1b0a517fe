[  9%] Built target legged_controllers_gencfg
[  9%] Built target roscpp_generate_messages_lisp
[  9%] Built target roscpp_generate_messages_nodejs
[  9%] Built target roscpp_generate_messages_cpp
[  9%] Built target roscpp_generate_messages_eus
[  9%] Built target rosgraph_msgs_generate_messages_cpp
[  9%] Built target rosgraph_msgs_generate_messages_eus
[  9%] Built target roscpp_generate_messages_py
[  9%] Built target rosgraph_msgs_generate_messages_lisp
[  9%] Built target rosgraph_msgs_generate_messages_nodejs
[  9%] Built target std_msgs_generate_messages_eus
[  9%] Built target std_msgs_generate_messages_cpp
[  9%] Built target rosgraph_msgs_generate_messages_py
[  9%] Built target std_msgs_generate_messages_nodejs
[  9%] Built target ocs2_msgs_generate_messages_py
[  9%] Built target std_msgs_generate_messages_lisp
[  9%] Built target ocs2_msgs_generate_messages_cpp
[  9%] Built target std_msgs_generate_messages_py
[  9%] Built target legged_interface_gencfg
[  9%] Built target ocs2_msgs_generate_messages_eus
[  9%] Built target ocs2_msgs_generate_messages_nodejs
[  9%] Built target visualization_msgs_generate_messages_cpp
[  9%] Built target visualization_msgs_generate_messages_eus
[  9%] Built target visualization_msgs_generate_messages_nodejs
[  9%] Built target visualization_msgs_generate_messages_lisp
[  9%] Built target ocs2_msgs_generate_messages_lisp
[  9%] Built target geometry_msgs_generate_messages_cpp
[  9%] Built target geometry_msgs_generate_messages_lisp
[  9%] Built target visualization_msgs_generate_messages_py
[  9%] Built target geometry_msgs_generate_messages_nodejs
[  9%] Built target geometry_msgs_generate_messages_eus
[  9%] Built target actionlib_generate_messages_cpp
[  9%] Built target geometry_msgs_generate_messages_py
[  9%] Built target actionlib_generate_messages_lisp
[  9%] Built target actionlib_generate_messages_eus
[  9%] Built target actionlib_generate_messages_py
[  9%] Built target actionlib_generate_messages_nodejs
[  9%] Built target actionlib_msgs_generate_messages_cpp
[  9%] Built target actionlib_msgs_generate_messages_eus
[  9%] Built target actionlib_msgs_generate_messages_py
[  9%] Built target tf2_msgs_generate_messages_eus
[  9%] Built target actionlib_msgs_generate_messages_nodejs
[  9%] Built target tf2_msgs_generate_messages_cpp
[  9%] Built target actionlib_msgs_generate_messages_lisp
[  9%] Built target tf2_msgs_generate_messages_lisp
[  9%] Built target dynamic_reconfigure_generate_messages_eus
[  9%] Built target dynamic_reconfigure_generate_messages_cpp
[  9%] Built target tf2_msgs_generate_messages_py
[  9%] Built target tf_generate_messages_eus
[  9%] Built target tf2_msgs_generate_messages_nodejs
[  9%] Built target tf_generate_messages_cpp
[  9%] Built target dynamic_reconfigure_generate_messages_nodejs
[  9%] Built target dynamic_reconfigure_generate_messages_lisp
[  9%] Built target dynamic_reconfigure_gencfg
[  9%] Built target tf_generate_messages_lisp
[  9%] Built target tf_generate_messages_nodejs
[  9%] Built target dynamic_reconfigure_generate_messages_py
[  9%] Built target tf_generate_messages_py
[  9%] Built target sensor_msgs_generate_messages_cpp
[  9%] Built target sensor_msgs_generate_messages_lisp
[  9%] Built target sensor_msgs_generate_messages_nodejs
[  9%] Built target sensor_msgs_generate_messages_py
[  9%] Built target sensor_msgs_generate_messages_eus
[  9%] Built target gsmp_msgs_generate_messages_cpp
[  9%] Built target gsmp_msgs_generate_messages_eus
[  9%] Built target gsmp_msgs_generate_messages_nodejs
[  9%] Built target gsmp_msgs_generate_messages_lisp
[  9%] Built target gsmp_msgs_generate_messages_py
[ 18%] [32mBuilding CXX object CMakeFiles/legged_controllers.dir/src/LeggedController_history.cpp.o[0m
[ 27%] [32m[1mLinking CXX shared library /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/legged_controllers/lib/liblegged_controllers.so[0m
[ 81%] Built target legged_controllers
[ 90%] [32m[1mLinking CXX executable /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/legged_controllers/lib/legged_controllers/legged_target_trajectories_publisher[0m
[100%] Built target legged_target_trajectories_publisher
