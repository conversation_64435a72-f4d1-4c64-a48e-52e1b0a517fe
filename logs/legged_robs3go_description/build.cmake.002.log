Not searching for unused variables given on the command line.
[36m--[0m The C compiler identification is GNU 11.4.0
[36m--[0m The CXX compiler identification is GNU 11.4.0
[36m--[0m Detecting C compiler ABI info
[36m--[0m Detecting C compiler ABI info - done
[36m--[0m Check for working C compiler: /usr/bin/cc - skipped
[36m--[0m Detecting C compile features
[36m--[0m Detecting C compile features - done
[36m--[0m Detecting CXX compiler ABI info
[36m--[0m Detecting CXX compiler ABI info - done
[36m--[0m Check for working CXX compiler: /usr/bin/c++ - skipped
[36m--[0m Detecting CXX compile features
[36m--[0m Detecting CXX compile features - done
[36m--[0m Using CATKIN_DEVEL_PREFIX: /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel/.private/legged_robs3go_description
[36m--[0m Using CMAKE_PREFIX_PATH: /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel;/opt/ros/noetic
[36m--[0m This workspace overlays: /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/devel;/opt/ros/noetic
[33mCMake Warning (dev) at /opt/ros/noetic/share/catkin/cmake/python.cmake:4 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

[36mCall Stack (most recent call first):[0m
  /opt/ros/noetic/share/catkin/cmake/all.cmake:164 (include)
  /opt/ros/noetic/share/catkin/cmake/catkinConfig.cmake:20 (include)
  CMakeLists.txt:5 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[36m--[0m Found PythonInterp: /usr/local/bin/python3 (found suitable version "3.8.10", minimum required is "3")
[36m--[0m Using PYTHON_EXECUTABLE: /usr/local/bin/python3
[36m--[0m Using Debian Python package layout
[36m--[0m Found PY_em: /usr/lib/python3/dist-packages/em.py
[36m--[0m Using empy: /usr/lib/python3/dist-packages/em.py
[36m--[0m Using CATKIN_ENABLE_TESTING: ON
[36m--[0m Call enable_testing()
[36m--[0m Using CATKIN_TEST_RESULTS_DIR: /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description/test_results
[36m--[0m Forcing gtest/gmock from source, though one was otherwise available.
[36m--[0m Found gtest sources under '/usr/src/googletest': gtests will be built
[36m--[0m Found gmock sources under '/usr/src/googletest': gmock will be built
[0mCMake Deprecation Warning at /usr/src/googletest/CMakeLists.txt:4 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.

[0m
[0mCMake Deprecation Warning at /usr/src/googletest/googlemock/CMakeLists.txt:45 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.

[0m
[0mCMake Deprecation Warning at /usr/src/googletest/googletest/CMakeLists.txt:56 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  CMake that the project does not need compatibility with older versions.

[0m
[33mCMake Warning (dev) at /usr/src/googletest/googletest/cmake/internal_utils.cmake:249 (find_package):
  Policy CMP0148 is not set: The FindPythonInterp and FindPythonLibs modules
  are removed.  Run "cmake --help-policy CMP0148" for policy details.  Use
  the cmake_policy command to set the policy and suppress this warning.

[36mCall Stack (most recent call first):[0m
  /usr/src/googletest/googletest/CMakeLists.txt:91 (include)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[36m--[0m Found PythonInterp: /usr/local/bin/python3 (found version "3.8.10")
[36m--[0m Found Threads: TRUE
[36m--[0m Using Python nosetests: /usr/bin/nosetests3
[36m--[0m catkin 0.8.12
[36m--[0m BUILD_SHARED_LIBS is on
[36m--[0m Configuring done (2.0s)
[36m--[0m Generating done (0.0s)
[36m--[0m Build files have been written to: /media/gjy/pan1/gao/reinforce-learning/rl-mpc-code-src/build/legged_robs3go_description
