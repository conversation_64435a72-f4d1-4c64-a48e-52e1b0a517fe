[  0%] Built target tf_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target visualization_msgs_generate_messages_py
[  0%] Built target tf_generate_messages_py
[  0%] Built target tf_generate_messages_nodejs
[  0%] Built target tf_generate_messages_lisp
[  0%] Built target tf_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target roscpp_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_cpp
[  0%] Built target roscpp_generate_messages_eus
[  0%] Built target roscpp_generate_messages_py
[  0%] Built target rosgraph_msgs_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_nodejs
[  0%] Built target rosgraph_msgs_generate_messages_eus
[  0%] Built target sensor_msgs_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_py
[  0%] Built target sensor_msgs_generate_messages_py
[  0%] Built target sensor_msgs_generate_messages_lisp
[  0%] Built target sensor_msgs_generate_messages_eus
[  0%] Built target sensor_msgs_generate_messages_nodejs
[  0%] Built target actionlib_generate_messages_nodejs
[  0%] Built target actionlib_msgs_generate_messages_lisp
[  0%] Built target actionlib_generate_messages_cpp
[  0%] Built target actionlib_generate_messages_py
[  0%] Built target actionlib_generate_messages_lisp
[  0%] Built target actionlib_generate_messages_eus
[  0%] Built target actionlib_msgs_generate_messages_eus
[  0%] Built target actionlib_msgs_generate_messages_cpp
[  0%] Built target _catkin_empty_exported_target
[  0%] Built target actionlib_msgs_generate_messages_nodejs
[  0%] Built target tf2_msgs_generate_messages_cpp
[  0%] Built target actionlib_msgs_generate_messages_py
[  0%] Built target tf2_msgs_generate_messages_nodejs
[  0%] Built target ocs2_msgs_generate_messages_lisp
[  0%] Built target tf2_msgs_generate_messages_lisp
[  0%] Built target tf2_msgs_generate_messages_py
[  0%] Built target tf2_msgs_generate_messages_eus
[  0%] Built target ocs2_msgs_generate_messages_eus
[  0%] Built target ocs2_msgs_generate_messages_cpp
[  0%] Built target visualization_msgs_generate_messages_eus
[  0%] Built target ocs2_msgs_generate_messages_py
[  0%] Built target visualization_msgs_generate_messages_nodejs
[  0%] Built target visualization_msgs_generate_messages_lisp
[  0%] Built target ocs2_msgs_generate_messages_nodejs
[  0%] Built target visualization_msgs_generate_messages_cpp
[ 25%] Built target ocs2_legged_robot_ros
[ 37%] Built target legged_robot_gait_command
[ 50%] Built target legged_robot_target
[ 62%] Built target legged_robot_ipm_mpc
[ 75%] Built target legged_robot_dummy
[ 87%] Built target legged_robot_sqp_mpc
[100%] Built target legged_robot_ddp_mpc
