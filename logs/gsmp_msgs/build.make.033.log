[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target _gsmp_msgs_generate_messages_check_deps_gl_stairmodeCmd
[  0%] Built target _gsmp_msgs_generate_messages_check_deps_gl_quadbotCmd
[  0%] Built target _gsmp_msgs_generate_messages_check_deps_HeadCmdData
[  0%] Built target _gsmp_msgs_generate_messages_check_deps_gl_quadbotState
[  0%] Built target _gsmp_msgs_generate_messages_check_deps_complex_command
[  0%] Built target _gsmp_msgs_generate_messages_check_deps_legged_robot_state
[ 21%] Built target gsmp_msgs_generate_messages_py
[ 40%] Built target gsmp_msgs_generate_messages_lisp
[ 62%] Built target gsmp_msgs_generate_messages_eus
[ 90%] Built target gsmp_msgs_generate_messages_nodejs
[100%] Built target gsmp_msgs_generate_messages_cpp
[100%] Built target gsmp_msgs_generate_messages
