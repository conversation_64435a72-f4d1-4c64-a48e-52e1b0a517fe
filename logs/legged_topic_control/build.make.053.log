[  0%] Built target geometry_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_py
[  0%] Built target geometry_msgs_generate_messages_py
[  0%] Built target gsmp_msgs_generate_messages_py
[  0%] Built target gsmp_msgs_generate_messages_cpp
[  0%] Built target geometry_msgs_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_cpp
[  0%] Built target std_msgs_generate_messages_eus
[  0%] Built target gsmp_msgs_generate_messages_nodejs
[  0%] Built target geometry_msgs_generate_messages_lisp
[  0%] Built target std_msgs_generate_messages_nodejs
[  0%] Built target gsmp_msgs_generate_messages_eus
[  0%] Built target std_msgs_generate_messages_lisp
[  0%] Built target geometry_msgs_generate_messages_nodejs
[  0%] Built target topic_tools_generate_messages_py
[  0%] Built target gsmp_msgs_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_cpp
[  0%] Built target roscpp_generate_messages_py
[  0%] Built target roscpp_generate_messages_eus
[  0%] Built target roscpp_generate_messages_lisp
[  0%] Built target rosgraph_msgs_generate_messages_lisp
[  0%] Built target roscpp_generate_messages_nodejs
[  0%] Built target rosgraph_msgs_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_nodejs
[  0%] Built target tf_generate_messages_eus
[  0%] Built target rosgraph_msgs_generate_messages_eus
[  0%] Built target tf_generate_messages_cpp
[  0%] Built target rosgraph_msgs_generate_messages_py
[  0%] Built target tf_generate_messages_lisp
[  0%] Built target tf_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_cpp
[  0%] Built target tf_generate_messages_py
[  0%] Built target actionlib_generate_messages_eus
[  0%] Built target sensor_msgs_generate_messages_eus
[  0%] Built target sensor_msgs_generate_messages_lisp
[  0%] Built target sensor_msgs_generate_messages_nodejs
[  0%] Built target sensor_msgs_generate_messages_py
[  0%] Built target actionlib_generate_messages_cpp
[  0%] Built target actionlib_generate_messages_lisp
[  0%] Built target actionlib_msgs_generate_messages_lisp
[  0%] Built target actionlib_generate_messages_nodejs
[  0%] Built target tf2_msgs_generate_messages_eus
[  0%] Built target actionlib_generate_messages_py
[  0%] Built target actionlib_msgs_generate_messages_py
[  0%] Built target actionlib_msgs_generate_messages_cpp
[  0%] Built target actionlib_msgs_generate_messages_eus
[  0%] Built target tf2_msgs_generate_messages_py
[  0%] Built target actionlib_msgs_generate_messages_nodejs
[  0%] Built target tf2_msgs_generate_messages_cpp
[  0%] Built target tf2_msgs_generate_messages_lisp
[  0%] Built target ocs2_msgs_generate_messages_nodejs
[  0%] Built target ocs2_msgs_generate_messages_cpp
[  0%] Built target ocs2_msgs_generate_messages_lisp
[  0%] Built target tf2_msgs_generate_messages_nodejs
[  0%] Built target ocs2_msgs_generate_messages_eus
[  0%] Built target ocs2_msgs_generate_messages_py
[  0%] Built target visualization_msgs_generate_messages_cpp
[  0%] Built target visualization_msgs_generate_messages_eus
[  0%] Built target visualization_msgs_generate_messages_nodejs
[  0%] Built target visualization_msgs_generate_messages_lisp
[  0%] Built target std_srvs_generate_messages_cpp
[  0%] Built target visualization_msgs_generate_messages_py
[  0%] Built target std_srvs_generate_messages_eus
[  0%] Built target std_srvs_generate_messages_lisp
[  0%] Built target std_srvs_generate_messages_py
[  0%] Built target std_srvs_generate_messages_nodejs
[  0%] Built target topic_tools_generate_messages_eus
[  0%] Built target topic_tools_generate_messages_cpp
[  0%] Built target topic_tools_generate_messages_lisp
[  0%] Built target topic_tools_generate_messages_nodejs
[  0%] Built target _legged_topic_control_generate_messages_check_deps_legged_robot_state
[  0%] Built target _legged_topic_control_generate_messages_check_deps_complex_command
[  9%] Built target legged_topic_control_generate_messages_cpp
[ 22%] Built target legged_topic_control_generate_messages_eus
[ 31%] Built target legged_topic_control_generate_messages_lisp
[ 40%] Built target legged_topic_control_generate_messages_nodejs
[ 54%] Built target legged_topic_control_generate_messages_py
[ 54%] Built target legged_topic_control_generate_messages
[ 72%] Built target legged_topic_control
[ 81%] Built target amp_data_record
[ 90%] Built target legged_robot_gait_command
[100%] Built target legged_robot_topic_control
