//
// Created by <PERSON><PERSON><PERSON> on 2022/6/24.
//

#pragma once

#include <controller_interface/multi_interface_controller.h>
#include <hardware_interface/imu_sensor_interface.h>
#include <legged_common/hardware_interface/ContactSensorInterface.h>

#include <ocs2_centroidal_model/CentroidalModelRbdConversions.h>
#include <ocs2_core/misc/Benchmark.h>
#include <legged_interface/visualization/LeggedRobotVisualizer.h>
#include <ocs2_mpc/MPC_MRT_Interface.h>

#include <legged_estimation/StateEstimateBase.h>
#include <legged_interface/LeggedInterface.h>
#include <legged_wbc/WbcBase.h>

#include "legged_controllers/SafetyChecker.h"
#include "legged_controllers/visualization/LeggedSelfCollisionVisualization.h"

#include "std_msgs/Float64MultiArray.h"
#include "std_msgs/Int32MultiArray.h"
#include <atomic>
#include "std_msgs/Float32.h"
#include "std_msgs/Int32.h"
#include <std_msgs/Bool.h>

#include <dynamic_reconfigure/server.h>
#include "legged_controllers/TutorialsConfig.h"
#include "legged_controllers/SingleLegKinematic.h"
#include <ocs2_robotic_tools/common/LoopshapingRobotInterface.h>
#include <sensor_msgs/Joy.h>
#include "ocs2_core/thread_support/BufferedValue.h"

#include "legged_controllers/DataReader.h"
#include "legged_controllers/BackFlipCtrl.hpp"
#include <iostream>
#include <fstream>

//RL
#include <onnxruntime/core/session/onnxruntime_cxx_api.h>
// #include <onnxruntime/onnxruntime_cxx_api.h>
#include <Eigen/Geometry>
#include <Eigen/Dense>
#include "legged_controllers/RLControllerCfg.h"
//stair_msg
// #include "legged_controllers/stair_Data.h"
#include <gsmp_msgs/gl_stairmodeCmd.h>
#include <gsmp_msgs/legged_robot_state.h>
#include <gsmp_msgs/gl_quadbotState.h>
#include <gsmp_msgs/gl_quadbotCmd.h>


namespace legged {
using namespace ocs2;
using namespace legged_robot;
using tensor_element_t = float;


struct LoopShapingConfig
{
  scalar_t A = 0;
  scalar_t B = 0;
  scalar_t C = 0;
  scalar_t D = 0;
  scalar_t R = 0;
};


class LeggedControllerHistory : public controller_interface::MultiInterfaceController<HybridJointInterface, hardware_interface::ImuSensorInterface,
                                                                               ContactSensorInterface> {
 public:
  // LeggedControllerHistory() = default;
  LeggedControllerHistory()
    :foot_data_buf_{std::move(sensor_msgs::Joy())},
    est_vel_local_rl_(std::move(vector_t::Zero(3))),
    rot_mat_buffer_rl_(std::move(matrix3_t::Zero()))
  {}
  ~LeggedControllerHistory() override;
  bool init(hardware_interface::RobotHW* robot_hw, ros::NodeHandle& controller_nh) override;
  void update(const ros::Time& time, const ros::Duration& period) override;
  void starting(const ros::Time& time) override;
  void stopping(const ros::Time& /*time*/) override
  {
    ros::param::set("gsmp_controller_state","stop");
    mpcRunning_ = false;
  }
    //Emergency stop
  ros::Subscriber sub_emgstop;
  ros::Subscriber sub_loadcontroller;
  ros::Subscriber sub_loadstart;
  ros::Subscriber sub_testLiftedTopic;



  void EmergencyStopCallback(const std_msgs::Float32::ConstPtr& msg);
  void LoadControllerCallback(const std_msgs::Float32::ConstPtr& msg);
  void LoadStartCallback(const std_msgs::Float32::ConstPtr& msg);
  void testLiftedTopic(const std_msgs::Float32::ConstPtr& msg)
  {
    runningLiftedMode = !runningLiftedMode;
    cmdLiftedJointPos_ = jointPos_;
    enterLiftedJointPos_ = jointPos_;
    enterLiftedTime = ros::Time::now();
  }

  void customPositionControlCallback(const std_msgs::Float64MultiArray::ConstPtr& msg)
  {
    // msg->data[0] means mode number, msg->data[1] means transition time
    if(runningLiftedMode)
      return;

    runningPositionMode = !runningPositionMode;

    double pos_transition_time = 2;
    if(runningPositionMode) {
      if(msg->data[1] <= 1)
        pos_transition_time = 1;
      else if(msg->data[1] >= 20)
        pos_transition_time = 20;
      else
        pos_transition_time = msg->data[1];

      onEnterPosMode(int(std::ceil(msg->data[0])), pos_transition_time);
    } else {
      onExitPosMode();
    }
  }

  void twoLegsStandCallback(const std_msgs::Float32::ConstPtr& msg) {
    if (int(std::ceil(msg->data)) <= 0) {
      twoLegStandMode_ = 0;
    } else if (int(std::ceil(msg->data)) >= 5) {
      twoLegStandMode_ = 5;
    } else {
      twoLegStandMode_ = int(std::ceil(msg->data));
    }
    onEnterTwoLegStandMode();
  }

  void backFlipCallback(const std_msgs::Float32::ConstPtr& msg) {
    onEnterBackFlipMode();
  }

  void dataRecordActionCallback(const std_msgs::Float32::ConstPtr& msg) {
    dataRecordFlag_ = !dataRecordFlag_;
    if(dataRecordFlag_){
      recordDataTime =  ros::Time::now();
      ROS_INFO_STREAM("###############DATA RECORD STRING##########");
    }
    else
    {
      ofs_amp.close();
      ROS_INFO_STREAM("###############DATA RECORD OVER##########");
    }
  }

  void FootSensorCallBack(const sensor_msgs::Joy::ConstPtr &msg)
  {
    static bool firstRecordTime_ = true;
    static bool footThresholdRecordFlag_ = true;
    static int dcount_ = 0;
    foot_data_buf_.setBuffer(*msg);

    if(firstRecordTime_){
      firstFootThresholdRecordTimer_.startTimer();
      firstRecordTime_ = false;
    }

    // first 2S get shreshold
    if(footThresholdRecordFlag_){
      foot_data_buf_.updateFromBuffer();
      auto footD_ = foot_data_buf_.get();
      // std::cout<<" footD_: "<<footD_.buttons[0]<<" "<<footD_.buttons[1]<<" "<<footD_.buttons[2]<<" "<<footD_.buttons[3]<<std::endl;

      for(size_t i =0 ;i<4;i++){
        footThreshold_(i) +=  footD_.buttons[i];
      }
      ++dcount_;
      firstFootThresholdRecordTimer_.endTimer();
      if(firstFootThresholdRecordTimer_.getLastIntervalInMilliseconds()/1000.0 > 2){
        for(size_t i=0;i<4;i++){
          footThreshold_(i) /= dcount_;
        }
        footThresholdRecordFlag_ = false;
        dcount_ = 0;
        realFootDataFlag_ = true;
      }
      // std::cout<<" time :"<<firstFootThresholdRecordTimer_.getLastIntervalInMilliseconds()/1000.0<<std::endl;
      // std::cout<<"dcount_:"<<dcount_<<std::endl;
      std::cout<<"FOOT DATA THRESHOLD: "<<footThreshold_[0]<<" "<<footThreshold_[1]<<" "<<footThreshold_[2]<<" "<<footThreshold_[3]<<std::endl;
    }
  }

  /////for RL controller added by WangC
  enum class Mode : uint8_t
    {
      LIE,
      STAND,
      WALK,
      DEFAULT,
      UPSTAIR,
      FREEZE,
      RLSTAND
    };
  
  //controller_switch
  ros::Subscriber controller_switch_sub;
  ros::Subscriber upstairModeSub_;
  //RL controller
  bool RLController_init(hardware_interface::RobotHW *robotHw, ros::NodeHandle &controllerNH);
  /////for RL controller added by WangC
  
  //add by gjy
  enum class TwohandMode : uint8_t
    {
      STAND,
      TILT,
      WALK,
    };


  void RotStateCallBack(const gsmp_msgs::legged_robot_state::ConstPtr &state){
    // 什么情况下不能调用: runningLiftedMode， controller_switch_flag, runningTwoLegStandMode
    std::cout<<" RotStateCallBack"<<std::endl;
    if(runningLiftedMode || controller_switch_flag ||runningTwoLegStandMode || runningBackFlipMode)
      return ;

    lastRotState = curRotState;
    lastControState = curControState;
    curRotState = state->curState;
    curControState = state->controState;

    ROS_INFO_STREAM("*******Get des pos RotStateCallBack:"<<curRotState);
    // 记录当前各关节值
    if(curRotState == "getdown" || curRotState == "sitdown_mode" ||
        curRotState == "handshake_right_mode" || curRotState == "handshake_left_mode" ||
        curRotState == "standup"){
      runningTransMode = true;
      // enterTransModeTime = ros::Time::now();
      enterTransModeTime = std::chrono::high_resolution_clock::now();
      enterTransModeJointPos_ = jointPos_;
      mpcRunning_ = false;
      std::cout<<" call back phase_transpos:"<<phase_transpos<<std::endl;
      phase_transpos = 0;
      ros::param::set("gsmp_trans_model_state","doing");
      transFinished = false;

      runningPositionMode = true;

    }


    if(curRotState == "getdown"){
      desTransModeJointPos_ = desGetDownModeJointPos_;
      if(robotStateCallBack){
        load_controller_flag = true;
        robotStateCallBack = false;
      }
    }
    else if(curRotState == "sitdown_mode"){
      desTransModeJointPos_ = desSitDownModeJointPos_;
      // std::cout<<" get des pos sitdown_mode"<<std::endl;

    }
    else if(curRotState == "handshake_right_mode" ){
      desTransModeJointPos_ = desHandShakeRightModeJointPos_;

    }
    else if(curRotState == "handshake_left_mode"){
      desTransModeJointPos_ = desHandShakeLeftModeJointPos_;

    }
    else if( curRotState == "standup"){
      desTransModeJointPos_ = standupModeJointPos_;
      if(lastControState == "on"){
        mpcRunning_ = true;
        runningTransMode = false;
        runningPositionMode = false;
        // get_node()->set_parameter(rclcpp::Parameter("gsmp_trans_model_state","done"));
        ros::param::set("gsmp_trans_model_state","done");
      }

    }

    // robotStateCallBack = false;


    // else if( curRotState == "standup"){
    //   if (std::abs(measuredRbdState_[2]) > 0.1 ||
    //     std::abs(measuredRbdState_[1]) > 0.1 ){
    //       mpcRunning_ = false;
    //       emergency_flag = true;

    //       return ;

    //     }
    //   currentObservation_.input.setZero(leggedInterface_->getCentroidalModelInfo().inputDim);
    //   TargetTrajectories target_trajectories({currentObservation_.time}, {currentObservation_.state}, {currentObservation_.input});
    //   currentObservation_.mode = ModeNumber::STANCE;
    //   mpcMrtInterface_->getReferenceManager().setTargetTrajectories(target_trajectories);

    //   mpcRunning_ = true;
    //   runningTransMode = false;
    //   runningPositionMode = false;
    //   // get_node()->set_parameter(rclcpp::Parameter("gsmp_trans_model_state","done"));
    //   ros::param::set("gsmp_trans_model_state","done");


    // }

  }
  void gaitTypeCallback(const std_msgs::Int32MultiArray::ConstPtr& msg){
    if(msg->data[0] == 20) {
      isPassiveTrot_.store(true);
      // gait_type_ = 1;
    } else {
      isPassiveTrot_.store(false);
    }
  }


  void quadbotStateCallback(const gsmp_msgs::gl_quadbotState::ConstPtr& msg) {
    action_name = msg->action_name;
    action_state = msg->action_state;
  }

  void switchTostandupMpcControl(){
    if (std::abs(measuredRbdState_[2]) > 0.6 ||
      std::abs(measuredRbdState_[1]) > 0.6 ){
        mpcRunning_ = false;
        emergency_flag = true;

        return ;

      }
    currentObservation_.input.setZero(leggedInterface_->getCentroidalModelInfo().inputDim);
    TargetTrajectories target_trajectories({currentObservation_.time}, {currentObservation_.state}, {currentObservation_.input});
    currentObservation_.mode = ModeNumber::STANCE;
    mpcMrtInterface_->getReferenceManager().setTargetTrajectories(target_trajectories);

    mpcRunning_ = true;
    runningTransMode = false;
    runningPositionMode = false;
    // get_node()->set_parameter(rclcpp::Parameter("gsmp_trans_model_state","done"));
    ros::param::set("gsmp_trans_model_state","done");

  }


  // body在机身坐标系下的线速度xy,以及ang_z
  void setEstVelLocalRl(vector_t est_plane_msg){
    est_vel_local_rl_.setBuffer(est_plane_msg);
  }
  // 世界到body的旋转矩阵
  void setRotMatRl(matrix3_t rot){
    rot_mat_buffer_rl_.setBuffer(rot);
  }
  void rlPassiveMode();


  void rollRecoveryCallback(const std_msgs::Float32::ConstPtr& msg) {
    // if(msg->data>0)
    rollRecoveryMode = msg->data>0? true: false;
    if(rollRecoveryMode){
      setControllerPassive(ros::Time::now());

      curRecoveryState = 0;
      recoveryCount_ = 0;
      kp_recovery_mode_ = 0;
      kd_recovery_mode_ = 1;
      recoveryStateStartTime = ros::Time::now();
      auto rollLast = currentObservation_.state(11);
      // if((rollLast>0&&rollLast<M_PI)||rollLast<0&&rollLast<-M_PI){ //left-up,right-down{
      if((rollLast>0&&rollLast<M_PI)||rollLast<-M_PI){ //left-up,right-down{

        if(!leftUpRightDownFlag_){
          leftUpRightDownFlag_ = true;
          rollRecoveryPosConvert();
        }
      }
      else if((rollLast>0&&rollLast>M_PI)||(rollLast<0&&rollLast>-M_PI)){
        if(leftUpRightDownFlag_){
          leftUpRightDownFlag_ = false;
          rollRecoveryPosConvert();
        }
      }
      std::cout<<" roll recovery call back!!"<<std::endl;
    }
  }

  void bodyAdjustmentAndRecovery(const SystemObservation& observation,const ros::Time& time);


  void rollRecoveryPosConvert(){
    // straightenJointState
    vector_t tmpPos_ = straightenTargetPos.segment<6>(0);
    straightenTargetPos.segment<6>(0) = straightenTargetPos.segment<6>(6);
    straightenTargetPos.segment<6>(6) = tmpPos_;
    straightenTargetPos(0) *=-1;
    straightenTargetPos(3) *=-1;
    straightenTargetPos(6) *=-1;
    straightenTargetPos(9) *=-1;
    // reverseStage1TargetPos
    tmpPos_ = reverseStage1TargetPos.segment<6>(0);
    reverseStage1TargetPos.segment<6>(0) = reverseStage1TargetPos.segment<6>(6);
    reverseStage1TargetPos.segment<6>(6) = tmpPos_;
    reverseStage1TargetPos(0) *=-1;
    reverseStage1TargetPos(3) *=-1;
    reverseStage1TargetPos(6) *=-1;
    reverseStage1TargetPos(9) *=-1;

    // reverseStage2TargetPos
    tmpPos_ = reverseStage2TargetPos.segment<6>(0);
    reverseStage2TargetPos.segment<6>(0) = reverseStage2TargetPos.segment<6>(6);
    reverseStage2TargetPos.segment<6>(6) = tmpPos_;
    reverseStage2TargetPos(0) *=-1;
    reverseStage2TargetPos(3) *=-1;
    reverseStage2TargetPos(6) *=-1;
    reverseStage2TargetPos(9) *=-1;
    // reverseStage3TargetPos
    tmpPos_ = reverseStage3TargetPos.segment<6>(0);
    reverseStage3TargetPos.segment<6>(0) = reverseStage3TargetPos.segment<6>(6);
    reverseStage3TargetPos.segment<6>(6) = tmpPos_;
    reverseStage3TargetPos(0) *=-1;
    reverseStage3TargetPos(3) *=-1;
    reverseStage3TargetPos(6) *=-1;
    reverseStage3TargetPos(9) *=-1;
    // reverseStage4TargetPos
    tmpPos_ = reverseStage4TargetPos.segment<6>(0);
    reverseStage4TargetPos.segment<6>(0) = reverseStage4TargetPos.segment<6>(6);
    reverseStage4TargetPos.segment<6>(6) = tmpPos_;
    reverseStage4TargetPos(0) *=-1;
    reverseStage4TargetPos(3) *=-1;
    reverseStage4TargetPos(6) *=-1;
    reverseStage4TargetPos(9) *=-1;
  }



 protected:
  virtual void updateStateEstimation(const ros::Time& time, const ros::Duration& period);

  virtual void setupMotorParam(const std::string& motorFile);

  virtual void setupLeggedInterface(const std::string& taskFile, const std::string& urdfFile, const std::string& referenceFile,
                                    bool verbose);
  virtual void setupMpc();
  virtual void setupMrt();
  virtual void setupStateEstimate(const std::string& taskFile, bool verbose);

  //for odom
  virtual void setupStateEstimateForRL(const std::string& taskFile, bool verbose);
  virtual void updateStateEstimationForRL(const ros::Time& time, const ros::Duration& period);


  vector_array_t computeFeetPosAndVel(const vector_t& qPino, const vector_t& vPino);


  // add for safety check and measure
  void limitJointPosAndTor(vector_t& jointPos,vector_t& jointTor,const scalar_t jointTorMax=40);
  void setControllerStop();
  void setControllerPassive(const ros::Time& time);

  bool checkWbcQpSolvedErr(const ros::Time& time,double durationTime=0.5,double ErrThre=0.3);
  bool checkMpcContactForceErr(const ros::Time& time, const vector_t& mpcContactForce,const int gaitType,double durationTime=0.5,double ErrThre=0.6);
  bool checkJointTorErr(const ros::Time& time,const vector_t& jointTorDes,double jointTorMax=10,double durationTime=0.5,double ErrThre=0.95);


  // add by henning on 2024-1-9, 拎起检测
  bool checkRobotLifted(const ros::Time& time, const vector_t& footPos, const contact_flag_t& cmdContactFlag);
  bool checkLiftedinRL(const ros::Time& time, const vector_t& footPos);
  void onEnterLiftedMode(const ros::Time& time);
  void onExitLiftedMode(const ros::Time& time);
  bool checkJointPosNear(const vector_t &pos1, const vector_t &pos2);
  vector_t jointTor_,jointPos_, jointVel_;
  vector_t cmdLiftedJointPos_;
  vector_t enterLiftedJointPos_;

  std::atomic_bool runningLiftedMode{false};

  ros::Time enterLiftedTime;
  ros::Time recordDataTime;

  double feet_bias_x = 0.1645;
  double feet_bias_y = 0.12;
  double feet_bias_z = -0.3;

  double max_foot_pos_z_lifted = 0.378;
  double max_leg_length_lifted = 0.41;
  double gravity_factor_lifted = 0.115;
  double gravity_factor_lifted_recover = 0.115;
  double k_lose_contact_factor_ = 0.166;
  double phase_factor_lifted = 0.3;

  double mode_transition_time_lifted = 3;
  double pos_transition_time_lifted = 4;

  bool debug_print_lifted = false;
  bool lifted_detection_enable = true;

  double hip_pos_lifted = 0.05;
  double thigh_pos_lifted = 0.65;
  double calf_pos_lifted = -1.395;

  ros::Publisher testLiftedPublisher_;
  ros::Subscriber stair_mode_sub_;
  // add by henning on 2024-1-9

  // add by henning on 2024-1-15, 趴下抱起模式
  std::atomic_bool runningPositionMode{false};
  std::atomic_bool dataRecordFlag_{false};

  ros::Subscriber custom_position_control_sub_;
  ros::Subscriber foot_sensor_sub_;
  BufferedValue<sensor_msgs::Joy> foot_data_buf_;
  vector_t cmdPosModeJointPos_;
  vector_t enterPosModeJointPos_;
  ros::Time enterPosModeTime;

  double pos_transition_time_ = 4;

  void onEnterPosMode(int mode, double transition_time);
  void onExitPosMode();

  vector_t hugMeBabyModeJointPos_;
  double kp_pos_mode_ = 85;
  double kd_pos_mode_ = 1;

  double kp_hug_me_baby_ = 85;
  double kd_hug_me_baby_ = 1;
  // add by henning on 2024-1-15, 趴下抱起模式


  // add by henning on 2024-1-18, 拜年动作
  double thigh_length_ = 0.2;
  double calf_length_ = 0.2;
  double calf_link_pitch_ = 0.0;
  SingleLegKinematic singleLegKinematic_;

  std::atomic_bool runningTwoLegStandMode{false};

  void runTwoLegStandAdjustStage(const ros::Time& time);
  void runTwoLegStandPushStage(const ros::Time& time);
  void runHappyNewYear(const ros::Time& time);// and so on movements
  void runTwoLegStandFinalStage(const ros::Time& time);
  void runTwoLegStandRecoverStage(const ros::Time& time);

  void onEnterTwoLegStandMode();
  void onExitTwoLegStandMode();

  // for adjust stage
  double des_thigh_pos_adjust_stage_f_ = 0.79;
  double des_calf_pos_adjust_stage_f_ = -1.95;
  double des_thigh_pos_adjust_stage_r_ = 0.79;
  double des_calf_pos_adjust_stage_r_ = -1.95;

  double kp_adjust_stage_ = 85;
  double kd_adjust_stage_ = 1.8;
  double adjust_duration_ = 1;

  // for push stage
  double push_force_init_ = 40;
  double push_force_target_ = 60;
  double exert_force_duration_ = 0.2;
  double push_duration_ = 0.6;
  double rear_legs_adjust_duration_ = 1;
  double rear_legs_keep_duration_ = 1.8;

  bool front_legs_force_to_pos_ = false;
  bool push_finished_[2]{false};

  double des_front_hip_pos_push_stage_ = -0.24497866;
  double des_front_thigh_pos_push_stage_ = 0.79;
  double des_front_calf_pos_push_stage_ = -1.95;
  double des_back_thigh_pos_push_stage_ = 0.79;
  double des_back_calf_pos_push_stage_ = -1.95;
  double kp_hip_exert_stage_ = 40;
  double kd_hip_exert_stage_ = 0.5;
  double kp_hip_push_stage_ = 85;
  double kp_thigh_push_stage_ = 85;
  double kp_calf_push_stage_ = 85;
  double kd_hip_push_stage_ = 1.8;
  double kd_thigh_push_stage_ = 1.8;
  double kd_calf_push_stage_ = 1.8;
  double torque_rear_thigh_push_stage_ = 16;
  double torque_rear_calf_push_stage_ = 16;
  double torque_rear_thigh_keep_stage_ = 5;

  double kp_hip_push_stage_rear_ = 55;
  double kp_thigh_push_stage_rear_ = 55;
  double kp_calf_push_stage_rear_ = 65;
  double kd_hip_push_stage_rear_ = 1.4;
  double kd_thigh_push_stage_rear_ = 2.0;
  double kd_calf_push_stage_rear_ = 2.0;

  double kp_hip_push_adjust_stage_rear_ = 85;
  double kp_thigh_push_adjust_stage_rear_ = 100;
  double kp_calf_push_adjust_stage_rear_ = 120;

  double kd_hip_push_adjust_stage_rear1_ = 1.5;
  double kd_thigh_push_adjust_stage_rear1_ = 2.0;
  double kd_calf_push_adjust_stage_rear1_ = 2.6;

  double kd_hip_push_adjust_stage_rear2_ = 1.5;
  double kd_thigh_push_adjust_stage_rear2_ = 5.0;
  double kd_calf_push_adjust_stage_rear2_ = 10.0;

  double happy_new_year_pos1_x_ = 0.05;
  double happy_new_year_pos1_y_ = -0.03;
  double happy_new_year_pos1_z_ = -0.22;

  double happy_new_year_pos2_x_ = -0.12;
  double happy_new_year_pos2_y_ = -0.03;
  double happy_new_year_pos2_z_ = -0.16;

  double happy_new_year_swing_duration_ = 0.6;

  double kp_hip_final_stage_ = 40;
  double kp_thigh_final_stage_ = 50;
  double kp_calf_final_stage_ = 56;

  double kd_hip_final_stage_ = 1.0;
  double kd_thigh_final_stage_ = 1.2;
  double kd_calf_final_stage_ = 1.35;

  double des_thigh_pos_recover_stage_ = 0.71203455;
  double des_calf_pos_recover_stage_ = -1.19599748;
  double kp_recover_stage_ = 40;
  double kd_recover_stage_ = 2;
  double recovery_duration_ = 2;

  int twoLegStandMode_ = 0; // default 0: for happy new
  int twoLegStandStage_ = 0; // adjust stage: 0, push stage: 1, happy new year and .....balabala little fairy

  ros::Subscriber two_legs_stand_sub_;

  ros::Time enterTwoLegStandModeTime;
  double twoLegStandModeLeaveGroundTime;
  double twoLegStandModeReachTargetTime;

  vector_t enterTwoLegStandModeJointPos_;

  vector_t cmdTwoLegStandModeJointPos_;
  vector_t cmdTwoLegStandModeTau_;
  vector_t cmdTwoLegStandModeKp_;
  vector_t cmdTwoLegStandModeKd_;
  // add by henning on 2024-1-18, 拜年动作

  bool checkLifted = false;
  bool checkLiftedRL = false;

  // add by henning on 2024-2-19 backflip mode
  void onEnterBackFlipMode();
  bool initializeBackFlipMode();
  void runBackFlipMode(const ros::Time& time);
  void onExitBackFlipMode();
  ros::Subscriber back_flip_sub_;
  std::atomic_bool runningBackFlipMode{false};

  LegControllerCommand<double> *backFlipCmd_;
  LegControllerData<double> *backFlipData_;
  BackFlipCtrl<double>* backFlipCtrl_;
  DataReader* dataReader_;

  vector_t enterBackFlipModeJointPos_;
  double curBackFlipModeTime = 0;
  unsigned int backFlipModeCount_ = 0;
  bool firstInBackFlip_ = true;

  vector_t cmdBackFlipModeJointPos_;     // = vector_t::Zero(hybridJointHandles_.size());
  vector_t cmdBackFlipModeJointVel_;     // = vector_t::Zero(hybridJointHandles_.size());
  vector_t cmdBackFlipModeTau_;          // = vector_t::Zero(hybridJointHandles_.size());
  vector_t cmdBackFlipModeKp_;           // = vector_t::Zero(hybridJointHandles_.size());
  vector_t cmdBackFlipModeKd_;           // = vector_t::Zero(hybridJointHandles_.size());

  double kp_adjust_stage_back_flip_ = 25;
  double kd_adjust_stage_back_flip_ = 2;
  double back_flip_adjust_duration_ = 2;
  double back_flip_adjust_thigh_pos_ = 1.25;
  double back_flip_adjust_calf_pos_ = -2.3;

  double kp_run_stage_back_flip_ = 40;
  double kd_run_stage_back_flip_ = 1.5;
  double torque_mult_back_flip_ = 5;

  double kp_final_stage_back_flip_ = 30;
  double kd_final_stage_back_flip_ = 1.1;

  double landing_thigh_pos_back_flip_f_ = 0.6;
  double landing_knee_pos_back_flip_f_ = -1.6;
  double landing_thigh_pos_back_flip_b_ = 0.6;
  double landing_knee_pos_back_flip_b_ = -1.6;
  // add by henning on 2024-2-19 backflip mode

  // Interface
  std::shared_ptr<LeggedInterface> leggedInterface_;
  std::shared_ptr<LoopshapingDefinition> loopshapingDefinitionPtr_;
  std::shared_ptr<LoopshapingRobotInterface> loopShapingLeggedInterface_;
  std::shared_ptr<PinocchioEndEffectorKinematics> eeKinematicsPtr_;
  std::vector<HybridJointHandle> hybridJointHandles_;
  std::vector<ContactSensorHandle> contactHandles_;
  hardware_interface::ImuSensorHandle imuSensorHandle_;

  // State Estimation
  SystemObservation currentObservation_;
  std::shared_ptr<StateEstimateBase> stateEstimate_;
  vector_t measuredRbdState_;
  std::shared_ptr<CentroidalModelRbdConversions> rbdConversions_;

    //for odom
  SystemObservation currentObservation_RL_;
  std::shared_ptr<StateEstimateBase> stateEstimateRL_;
  vector_t measuredRbdStateRL_;

  //for gsmp msg
  ros::Subscriber gsmp_quadbotSate_sub_;
  ros::Publisher gsmp_quadbotCmd_pub_;
  std::string action_name;
  uint8_t action_state;

  nav_msgs::Odometry mpcodom_msg_;
  nav_msgs::Odometry rlodom_msg_;

  ros::Subscriber mpc_odom_sub_;
  ros::Subscriber rl_odom_sub_;
  ros::Publisher odom_pub_;

  vector3_t mpc_zyx;
  vector3_t rl_zyx;
  Eigen::Quaternion<scalar_t> odom_quat_;

  // Whole Body Control
  std::shared_ptr<WbcBase> wbc_;
  std::shared_ptr<SafetyChecker> safetyChecker_;

  // Nonlinear MPC
  std::shared_ptr<MPC_BASE> mpc_;
  std::shared_ptr<MPC_MRT_Interface> mpcMrtInterface_;

  // Visualization
  std::shared_ptr<legged::LeggedRobotVisualizer> robotVisualizer_;
  std::shared_ptr<LeggedSelfCollisionVisualization> selfCollisionVisualization_;
  ros::Publisher observationPublisher_;

  // Control Data Analysis
  ros::Publisher estBodyPositionPublisher_;
  ros::Publisher estRLBodyPositionPublisher_;
  ros::Publisher cmdBodyPositionPublisher_;
  ros::Publisher mpcPlannedBodyPositionPublisher_;

  ros::Publisher wbcPlannedBodyAccelerationPublisher_;
  ros::Publisher imuMeasuredBodyAccelerationPublisher_;

  ros::Publisher estFeetPositionPublisher_;
  ros::Publisher estRLFeetPositionPublisher_;
  ros::Publisher mpcPlannedFeetPositionPublisher_;

  ros::Publisher estFeetVelocityPublisher_;
  ros::Publisher estRLFeetVelocityPublisher_;
  ros::Publisher mpcPlannedFeetVelocityPublisher_;

  ros::Publisher estContactForcePublisher_;
  ros::Publisher mpcPlannedContactForcePublisher_;
  ros::Publisher wbcPlannedContactForcePublisher_;

  ros::Publisher cmdContactStatePublisher_;
  ros::Publisher estContactStatePublisher_;
  ros::Publisher realContactStatePublisher_;

  ros::Publisher wbcPlannedTorquePublisher_;
  ros::Publisher outputTorquePublisher_;
  ros::Publisher realTorquePublisher_;
  ros::Publisher estDisturbanceTorquePublisher_;

  ros::Publisher realJointVelPublisher_;
  ros::Publisher mpcPlannedJointVelPublisher_;
  ros::Publisher wbcPlannedJointVelPublisher_;
  ros::Publisher filteredPlannedJointVelPublisher_;

  ros::Publisher realJointPosPublisher_;
  ros::Publisher mpcPlannedJointPosPublisher_;
  ros::Publisher wbcPlannedJointPosPublisher_;
  ros::Publisher filteredPlannedJointPosPublisher_;

  ros::Publisher wbcPlannedJointAccPublisher_;

  ros::Publisher adjustCentroid_pub_;


  vector_t qPino_;
  vector_t vPino_;
  vector_t qPino_RL_;
  vector_t vPino_RL_;

  ros::Duration starting_time_;
  ros::Duration last_time_;
  bool frist_init_finished = false;

  std::unique_ptr<dynamic_reconfigure::Server<legged_controllers::TutorialsConfig>> server_ptr_;


  // add for safety check by fzy
  std::vector<int> wbcQqErrCorNum_{0,0};
  std::array<std::vector<int>, 12> jointTorErrCorNum_;
  feet_array_t<std::vector<int>> mpcContactForceErrCorNum_;


  bool wbcQqErrStart_{false};
  bool mpcContactForceErrStart_{false};
  bool jointTorErrStart_{false};

  benchmark::RepeatedTimer wbcQqErrTimer_;
  benchmark::RepeatedTimer controlTimer_;
  benchmark::RepeatedTimer mpcContactForceErrTimer_;
  benchmark::RepeatedTimer jointTorErrTimer_;
  benchmark::RepeatedTimer firstFootThresholdRecordTimer_;
  vector_t footThreshold_;
  bool realFootDataFlag_ = false;

    //////for RL control added by WangC
  Mode mode_;
  int64_t loopCount_;
  Command command_;
  Command lstcommand_;
  RLRobotCfg robotCfg_{};
  JointState standjointState_{0.0, 0.9, -1.55,
                              0.0, 0.9, -1.55,
                              0.0, 0.9, -1.55,
                              0.0, 0.9, -1.55};

  JointState liejointState_{0.0, 1.57, -2.60,
                            0.0, 1.57, -2.60,
                            0.0, 1.57, -2.60,
                            0.0, 1.57, -2.60};
  std::atomic_bool emergency_stop{false};
  std::atomic_bool start_control{false};
  std::atomic_bool position_control{false};
  std::atomic_bool upStairsFlag{false};
  ros::Time switchTime;
  ros::Time cmdTime;

  //for RL controller add by WangC on 2024-9-27
  int actuatedDofNum_ = 12;
  int walkCount_ = 0;
  int RLType = 0;
  float cfg_kd;
  vector_t rlPos_;
  Proprioception propri_;

  ros::Subscriber cmdVelSub_;
  ros::Subscriber joyInfoSub_;
  ros::Subscriber emgStopSub_;
  ros::Subscriber startCtrlSub_;
  ros::Subscriber switchModeSub_;
  ros::Subscriber walkModeSub_;
  ros::Subscriber positionCtrlSub_;
  ros::Subscriber rlstandModeSub_;

  ros::Publisher rlPosPublisher_;
  ros::Publisher rlComputeTauPublisher_;
  ros::Publisher rlComputePosPublisher_;

  void handleLieMode();
  void handleStandMode();
  void handleDefautMode();
  void handleWalkMode();
  void handleFreezeMode();
  void handleRlstandMode();

  bool loadModel(ros::NodeHandle &nh);
  bool loadRLCfg(ros::NodeHandle &nh);
  void computeActions();
  void computeActions1();
  void computeUpstairActions();
  void computeObservation();
  void computeObservation1();
  void ControllerSwitchCallback(const std_msgs::Float32::ConstPtr& msg);
  void cmdVelCallback(const geometry_msgs::Twist &msg);
  void StairSwitchCallback(const gsmp_msgs::gl_stairmodeCmd& msg);
  // void joyInfoCallback(const sensor_msgs::Joy &msg);
  //for RL controller add by WangC on 2024-9-27

 private:
  std::thread mpcThread_;
  std::atomic_bool controllerRunning_{}, mpcRunning_{};
  benchmark::RepeatedTimer mpcTimer_;
  benchmark::RepeatedTimer wbcTimer_;

  int controller_msg_count{0};
  bool load_controller_flag{false};
  bool emergency_flag{false};
  bool gravity_compensation_flag{false};
  bool controller_switch_flag{false};
  bool switch_RL_flag{false};
  bool cmd_move_flag{true};

  vector_t pos_des_filtered_;
  vector_t vel_des_filtered_;
  vector_t tau_des_filtered_;
  vector_t MpcContactForce_;
  double avg_jointVel;
  double acc_mean;

  size_t state_dim_{};
  size_t input_dim_{};

  vector_t augmented_system_input_{};
  LoopShapingConfig contact_force_config_;
  LoopShapingConfig joint_velocity_config_;

  // henning 修改的斜坡估计
  Eigen::Matrix<scalar_t, 4, 3> W_pla_;
  Eigen::Vector3d plane_est_prev_;
  ros::Publisher plane_est_pub;
  std_msgs::Float64MultiArray plane_est_msg_;
  contact_flag_t estContactFlag{true};
  contact_flag_t realContactFlag{true};
  feet_array_t<vector3_t> latest_stance_position_{};

  // henning 修改的斜坡估计

  // add by fzy for ampdata
  std::ofstream ofs_amp;
  ros::Publisher bodyGobalVelPublisher_, bodyLocalVelPublisher_;
  ros::Subscriber dataRecordSub_;

  // add position control callback
  ros::Time pawupStartTimer_;
  vector_t enterPawupJointPos_;
  vector_t desPawupJointPosUp_;
  vector_t desPawupJointPosDown_;

  // legged_robot_state
  ros::Subscriber rot_state_sub_;
  std::string lastRotState = "getdown";
  std::string curRotState = "getdown";
  std::string curControState = "on";
  std::string lastControState = "on";
  std::atomic_bool runningTransMode{false};
  vector_t enterTransModeJointPos_;
  vector_t cmdTransModeJointPos_;
  vector_t desTransModeJointPos_;

  vector_t desGetDownModeJointPos_;
  vector_t desSitDownModeJointPos_;
  vector_t desHandShakeLeftModeJointPos_;
  vector_t desHandShakeRightModeJointPos_;
  vector_t standupModeJointPos_;
  vector_t dt_rl_xyz;
  // ros::Time enterTransModeTime;
  std::chrono::time_point<std::chrono::high_resolution_clock> enterTransModeTime;
  double phase_transpos=0;
  std::atomic_bool transFinished{false};

  //////for RL control added by WangC
  //run
  std::string policyModelPath_;
  std::string encoderModelPath_;
  std::string policyModelPath1;

  //upstaris
  std::string policyUpstairsModelPath_;
  std::string encoderUpstairsModelPath_;

  std::shared_ptr<Ort::Env> onnxEnvPrt_;

  //run
  std::unique_ptr<Ort::Session> policySessionPtr_;
  std::unique_ptr<Ort::Session> encoderSessionPtr_;
  std::unique_ptr<Ort::Session> policySessionPtr1_;

  //upstairs
  std::unique_ptr<Ort::Session> policyUpstairsSessionPtr_;
  std::unique_ptr<Ort::Session> encodeUpstairsSessionPtr_;


  std::vector<const char *> policyInputNames_;
  std::vector<const char *> policyOutputNames_;
  std::vector<const char *> policyInputNames1_;
  std::vector<const char *> policyOutputNames1_;



  std::vector<std::vector<int64_t>> policyInputShapes_;
  std::vector<std::vector<int64_t>> policyOutputShapes_;
  std::vector<std::vector<int64_t>> policyInputShapes1_;
  std::vector<std::vector<int64_t>> policyOutputShapes1_;



  std::vector<const char *> encoderInputNames_;
  std::vector<const char *> encoderOutputNames_;


  std::vector<std::vector<int64_t>> encoderInputShapes_;
  std::vector<std::vector<int64_t>> encoderOutputShapes_;

  std::vector<Ort::AllocatedStringPtr> inputPolicyNameAllocatedStrings;
  std::vector<Ort::AllocatedStringPtr> outputPolicyNameAllocatedStrings;
  std::vector<Ort::AllocatedStringPtr> inputPolicyNameAllocatedStrings1;
  std::vector<Ort::AllocatedStringPtr> outputPolicyNameAllocatedStrings1;

  std::vector<Ort::AllocatedStringPtr> inputEncoderNameAllocatedStrings;
  std::vector<Ort::AllocatedStringPtr> outputEncoderNameAllocatedStrings;

  vector3_t baseLinVel_;
  vector3_t basePosition_;
  vector_t lastActions_;
  vector_t defaultJointAnglesMPC_; //LF,LH,RF,RH
  vector_t defaultJointAnglesRL_; //LF,RF,LH,RH

  std::vector<scalar_t> currentJointAngles_;
  vector_t standJointAngles_;
  vector_t lieJointAngles_;

  vector_t pos_des_output_{};
  vector_t vel_des_output_{};

  size_t joint_dim_{0};

  scalar_t standPercent_;
  scalar_t standDuration_;

  bool isfirstRecObs_{true};
  int actionsSize_;
  int observationSize_;
  std::vector<float> actions_;
  std::vector<float> realActions_;
  std::vector<float> observations_;
  std::vector<float> stand_observations_;
  std::vector<float> history_observations_;
  std::vector<float> hidden_;
  std::vector<float> cell_;
  Eigen::Matrix<float, Eigen::Dynamic, 1> proprioHistoryBuffer_;
  //////for RL control added by WangC

  bool robotStateCallBack = true;

  // rl passive mode
  std::atomic_bool isPassiveTrot_;
  iir::LowpassFilterButterworth lowPassFilterX_;
  iir::LowpassFilterButterworth lowPassFilterY_;
  iir::LowpassFilterButterworth lowPassFilterYaw_;
  ros::Subscriber gait_type_sub_;
  BufferedValue<vector_t> est_vel_local_rl_;        // add by henning on 2023-10-11
  BufferedValue<matrix3_t> rot_mat_buffer_rl_;           // add by henning on 2023-10-11
  Eigen::Vector3d desVelXYZRl;


  // roll-recovery
  bool enterPassiveModel_=false;
  benchmark::RepeatedTimer passiveModelTimer_;



  ros::Subscriber roll_recovery_sub_;
  vector_t recoveryCurPos, recoveryTargetPos, shrinkTargetPos, straightenTargetPos;
  vector_t reverseStage1TargetPos, reverseStage2TargetPos, reverseStage3TargetPos, reverseStage4TargetPos, reverseStage5TargetPos;
  vector_t recoveryStageTime;

  std::atomic_bool rollRecoveryMode{false};
  std::atomic_int curRecoveryState{0};
  int recoveryCount_{0};
  ros::Time recoveryStateStartTime;
  double kp_recovery_mode_=0, kd_recovery_mode_=1;
  double kpRecovery_=20, kdRecovery_=1;
  double recoveryTime_ = 3.0;
  std::atomic_bool rollRecoveryTestMode{false};
  std::atomic_bool leftUpRightDownFlag_{true};


};

class LeggedCheaterController : public LeggedControllerHistory {
 protected:
  void setupStateEstimate(const std::string& taskFile, bool verbose) override;
};

}  // namespace legged
