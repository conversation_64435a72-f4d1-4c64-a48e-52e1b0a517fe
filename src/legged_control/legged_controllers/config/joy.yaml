teleop:
  walk:
    type: topic
    message_type: geometry_msgs/Twist
    topic_name: cmd_vel
    deadman_buttons: [4]
    axis_mappings:
      - axis: 3 
        target: angular.z
        scale: 1.5
      - axis: 1
        target: linear.x
        scale: 2.0
      - axis: 0
        target: linear.y
        scale: 0.5

  body_rotation:
    type: topic
    message_type: geometry_msgs/Twist
    topic_name: body_rotation
    deadman_buttons: [4]
    axis_mappings:
      - axis: 3
        target: angular.x
        scale: -0.4
      - axis: 1
        target: angular.y
        scale: 0.4
      - axis: 0
        target: angular.z  
        scale: 0.4

  bodyrotation_switch:
    type: topic
    message_type: std_msgs/Float32
    topic_name: bodyrotation_switch
    deadman_buttons: [ 4, 9 ]
    axis_mappings:
      - button: 9
        target: data
        scale: 2
        offset: 0

  load_controller:
    type: topic
    message_type: std_msgs/Float32
    topic_name: load_controller
    deadman_buttons: [ 7 ]
    axis_mappings:
      - button: 7
        target: data
        scale: 2
        offset: 0

  # stair_mode:
  #   type: topic
  #   message_type: std_msgs/Int32
  #   topic_name: stair_mode
  #   deadman_buttons: [ 6 ]
  #   axis_mappings:
  #     - button: 6
  #       target: data
  #       scale: 2
  #       offset: 0


  emergency_stop:
    type: topic
    message_type: std_msgs/Float32
    topic_name: emergency_stop
    deadman_buttons: [ 5, 1 ]
    axis_mappings:
      - button: 1
        target: data
        scale: 2
        offset: 0

  com_height_adjustment:
    type: topic
    message_type: std_msgs/Float32
    topic_name: com_height_adjustment
    deadman_buttons: [ 4 ]
    axis_mappings:
      - axis: 7
        target: data
        scale: 1
        offset: 0

  running_switch:
    type: topic
    message_type: std_msgs/Float32
    topic_name: running_switch
    deadman_buttons: [ 5, 2 ]
    axis_mappings:
      - button: 2
        target: data
        scale: 2
        offset: 1
  
  # stance_switch:
  #   type: topic
  #   message_type: std_msgs/Float32
  #   topic_name: stance_switch
  #   deadman_buttons: [ 5, 0 ]
  #   axis_mappings:
  #     - button: 0
  #       target: data
  #       scale: 2
  #       offset: 1

  # dynamicwalk_switch:
  #   type: topic
  #   message_type: std_msgs/Float32
  #   topic_name: dynamicwalk_switch
  #   deadman_buttons: [ 5, 3 ]
  #   axis_mappings:
  #     - button: 3
  #       target: data
  #       scale: 2
  #       offset: 1
  staticwalk_switch:
    type: topic
    message_type: std_msgs/Float32
    topic_name: staticwalk_switch
    deadman_buttons: [ 5, 3 ]
    axis_mappings:
      - button: 3
        target: data
        scale: 2
        offset: 1
  # fast_trot_switch:
  #   type: topic
  #   message_type: std_msgs/Float32
  #   topic_name: fast_trot_switch
  #   deadman_buttons: [ 5, 3 ]
  #   axis_mappings:
  #     - button: 3
  #       target: data
  #       scale: 2
  #       offset: 1

  swing_test:
    type: topic
    message_type: geometry_msgs/Twist
    topic_name: swing_cmd
    deadman_buttons: [ 5 ]
    axis_mappings:
      - axis: 4
        target: linear.z
        scale: 0.01
      - axis: 1
        target: linear.x
        scale: 0.08
      - axis: 0
        target: linear.y
        scale: 0.05

  swing_height_cmd:
    type: topic
    message_type: std_msgs/Float32
    topic_name: swing_height_cmd
    deadman_buttons: [ 4]
    axis_mappings:
      - axis: 6
        target: data
        scale: -1
        offset: 0

  imu_x_cmd:
    type: topic
    message_type: std_msgs/Float32
    topic_name: imu_x_cmd
    deadman_buttons: [ 5]
    axis_mappings:
      - axis: 7
        target: data
        scale: -1
        offset: 0

  imu_y_cmd:
    type: topic
    message_type: std_msgs/Float32
    topic_name: imu_y_cmd
    deadman_buttons: [ 5]
    axis_mappings:
      - axis: 6
        target: data
        scale: -1
        offset: 0
        
  joyToStandup:
    type: topic
    message_type: std_msgs/Float32
    topic_name: joyToStandup
    deadman_buttons: [ 5, 9 ]
    axis_mappings:
      - button: 9
        target: data
        scale: 2
        offset: 1

  joyToGetdown:
    type: topic
    message_type: std_msgs/Float32
    topic_name: joyToGetdown
    deadman_buttons: [ 5, 10 ]
    axis_mappings:
      - button: 10
        target: data
        scale: 2
        offset: 1

  joyToTrot:
    type: topic
    message_type: std_msgs/Float32
    topic_name: joyToTrot
    deadman_buttons: [ 5, 0 ]
    axis_mappings:
      - button: 0
        target: data
        scale: 2
        offset: 1

##### for RL control
  # rl_control:
  #   type: topic
  #   message_type: std_msgs/Float32
  #   topic_name: controller_switch
  #   deadman_buttons: [ 6 ]
  #   axis_mappings:
  #     - button: 6
  #       target: data
  #       scale: 2
  #       offset: 1

  joyToRl:
    type: topic
    message_type: std_msgs/Float32
    topic_name: joyToRl
    deadman_buttons: [ 6 ]
    axis_mappings:
      - button: 6
        target: data
        scale: 2
        offset: 1

  switch_mode:
    type: topic
    message_type: std_msgs/Float32
    topic_name: RL_switch_mode
    deadman_buttons: [4, 0]
    axis_mappings:
      - button: 0
        target: data
        scale: 2
        offset: 0

  walk_mode:
    type: topic
    message_type: std_msgs/Float32
    topic_name: RL_walk_mode
    deadman_buttons: [4,2]
    axis_mappings:
      - button: 2
        target: data
        scale: 2
        offset: 0

  position_control:
    type: topic
    message_type: std_msgs/Float32
    topic_name: RL_position_control
    deadman_buttons: [4,3]
    axis_mappings:
      - button: 3
        target: data
        scale: 2
        offset: 0

  rlstand_mode:
    type: topic
    message_type: std_msgs/Float32
    topic_name: rlstand_mode
    deadman_buttons: [4,1]
    axis_mappings:
      - button: 2
        target: data
        scale: 2
        offset: 0

  rlstand_mode:
    type: topic
    message_type: std_msgs/Float32
    topic_name: rlstand_mode
    deadman_buttons: [4,1]
    axis_mappings:
      - button: 2
        target: data
        scale: 2
        offset: 0