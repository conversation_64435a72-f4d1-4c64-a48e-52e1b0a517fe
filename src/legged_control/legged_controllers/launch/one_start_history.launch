<?xml version="1.0" ?>

<launch>
      <arg name="rviz" default="false"/>
      <arg name="record_data" default="false"/>

    <!-- 遥控v1 -->
    <include file="$(find legged_controllers)/launch/joy_teleop.launch"/>

    <arg name="robot_type" default="$(env ROBOT_TYPE)" doc="Robot type: [robsgo2, newgo, yscgo, a1, aliengo, go1, laikago, gsmp, xxgo, yobogo, avago,xiaomigo,betago, greengo,redgo]"/>

    <arg name="cheater" default="false"/>
    
    <!-- RL controller config -->
    <!-- <arg name="robot_policy_type" default="xxgo" doc="Robot policy type: [yobogo, aliengo, go1, laikago, gsmp, xxgo, yobogo, avago,xiaomigo,betago]"/> -->
    <arg name="robot_policy_type" default="$(env ROBOT_TYPE)"/>
    <!-- <arg name="robot_policy_type" default="ysc3go"/> -->
    
    <rosparam file="$(find legged_controllers)/config/$(arg robot_policy_type)/$(arg robot_policy_type)_kd.yaml" command="load"/>
    <rosparam file="$(find legged_controllers)/config/$(arg robot_policy_type)/$(arg robot_policy_type)_student.yaml" command="load"/>
    <!-- <param name="policyModelPath_default" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/policy_lstm_flat_0312.onnx"/>
    <param name="encoderModelPath_default" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/lstm_encoder_flat_0312.onnx"/> -->
    <param name="policyModelPath_default" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/policy.onnx"/>
    <param name="encoderModelPath_default" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/encoder.onnx"/>
    <param name="policyModelPath_0" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/policy.onnx"/>
    <param name="encoderModelPath_0" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/encoder.onnx"/>
    <param name="policyModelPath_1" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/policy.onnx"/>
    <param name="encoderModelPath_1" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/encoder.onnx"/>
    <param name="policyModelPath1" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/twohand_stand.onnx"/>
    <!-- <param name="policyModelPath1" value="$(find legged_controllers)/policy/ysc_stand/ysc_stand.onnx"/> -->
    <!-- <param name="policyModelPath_default" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/policy_lstm_ysc4go_gjy0526.onnx"/>
    <param name="encoderModelPath_default" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/lstm_encoder_ysc4go_gjy0526.onnx"/>
    <param name="policyModelPath_0" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/policy_lstm_ysc4go_gjy0526.onnx"/>
    <param name="encoderModelPath_0" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/lstm_encoder_ysc4go_gjy0526.onnx"/>
    <param name="policyModelPath_1" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/policy_lstm_ysc4go_gjy0526.onnx"/>
    <param name="encoderModelPath_1" value="$(find legged_controllers)/policy/$(arg robot_policy_type)/lstm_encoder_ysc4go_gjy0526.onnx"/>
    <param name="policyModelPath1" value="$(find legged_controllers)/policy/ysc_stand/ysc_stand.onnx"/> -->

    <!-- make the files into global parameters -->
    <param name="urdfFile" value="/tmp/legged_control/$(arg robot_type).urdf"/>
    <param name="taskFile" value="$(find legged_controllers)/config/$(arg robot_type)/task.info"/>
    <param name="referenceFile" value="$(find legged_controllers)/config/$(arg robot_type)/reference.info"/>
    <param name="gaitCommandFile" value="$(find legged_controllers)/config/$(arg robot_type)/gait.info"/>
    <param name="velLimitFile" value="$(find legged_controllers)/config/$(arg robot_type)/vel_limit.info"/>
    <!-- <param name="bodyRecoveryFile" value="$(find legged_controllers)/config/bodyRecovery_sim.info"/> -->
    <param name="bodyRecoveryFile" value="$(find legged_controllers)/config/bodyRecovery.info"/>

    <!-- for motor kp kd -->
    <param name="motorFile" value="$(find legged_controllers)/config/$(arg robot_type)/motor.info"/>
    <param name="trajDataFile" value="$(find legged_controllers)/data/q_qd_q2d.csv"/>
    <param name="collectDataDir" value="$(find legged_controllers)/data/"/>

    <rosparam file="$(find legged_controllers)/config/controllers.yaml" command="load"/>

    <node if="$(arg cheater)" name="controller_loader" pkg="controller_manager" type="spawner"
          output="screen" args="
          controllers/joint_state_controller
          controllers/legged_controller_history
          controllers/legged_cheater_controller
"/>

    <node unless="$(arg cheater)" name="controller_loader" pkg="controller_manager" type="spawner"
          output="screen" args="
          controllers/joint_state_controller
          controllers/legged_controller_history
"/>

    <!-- <node unless="$(arg cheater)" name="controller_loader" pkg="controller_manager" type="spawner"
          output="screen" args="
          controllers/joint_state_controller
          controllers/legged_closed_loop_leg_controller
"/> -->


    <!-- <node unless="$(arg cheater)" name="controller_loader" pkg="controller_manager" type="spawner"
          output="screen" args="
          controllers/joint_state_controller
          controllers/legged_closed_loop_leg_controller
"/> -->

    <!-- <node unless="$(arg cheater)" name="controller_loader" pkg="controller_manager" type="spawner"
          output="screen" args="
          controllers/joint_state_controller
          controllers/legged_motor_test_controller
"/> -->

    <!-- <node pkg="ocs2_legged_robot_ros" type="legged_robot_gait_command" name="legged_robot_gait_command"
          output="screen"/> -->

      <node pkg="legged_controllers" type="legged_target_trajectories_publisher" name="legged_robot_target"
            output="screen"/>
          
      <node pkg="legged_topic_control" type="legged_robot_topic_control" name="legged_robot_topic_control"
            output="screen"/>
      
      <node if="$(arg rviz)" name="rviz" pkg="rviz" type="rviz" args="-d $(find legged_controllers)/rviz/legged_robot.rviz" required="true" />
      
      <group if="$(arg record_data)">
            <include file="$(find legged_controllers)/launch/record_data.launch"/>
      </group>

      

</launch>